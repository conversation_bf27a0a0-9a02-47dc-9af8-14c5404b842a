"use strict";(()=>{var e={};e.id=7725,e.ids=[7725],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},7666:(e,r,t)=>{t.r(r),t.d(r,{config:()=>m,default:()=>d,routeModule:()=>g});var s={};t.r(s),t.d(s,{default:()=>u});var a=t(1802),i=t(7153),n=t(8781),o=t(7474),l=t(6482);async function u(e,r){let t=`suppliers-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;try{console.log(`[${t}] Suppliers API request:`,{method:e.method,query:e.query,userAgent:e.headers["user-agent"]});let s=await (0,o.Wg)(e);if(!s.valid)return console.log(`[${t}] Authentication failed:`,s.error),r.status(401).json({error:"Unauthorized",requestId:t});if(!["DEV","Admin"].includes(s.user.role))return console.log(`[${t}] Insufficient permissions:`,s.user.role),r.status(403).json({error:"Insufficient permissions",requestId:t});if("GET"===e.method)return await p(e,r,t);if("POST"===e.method)return await c(e,r,t,s.user);return r.status(405).json({error:"Method not allowed",requestId:t})}catch(e){return console.error(`[${t}] Suppliers API error:`,e),r.status(500).json({error:"Internal server error",message:e.message,requestId:t})}}async function p(e,r,t){try{let{search:s="",active:a="all",page:i=1,limit:n=50,sortBy:o="name",sortOrder:u="asc"}=e.query,p=l.supabaseAdmin.from("suppliers").select("*",{count:"exact"});s&&(p=p.or(`name.ilike.%${s}%,contact_person.ilike.%${s}%,email.ilike.%${s}%`)),"all"!==a&&(p=p.eq("is_active","true"===a));let c=["name","contact_person","email","created_at","updated_at"].includes(o)?o:"name";p=p.order(c,{ascending:"desc"!==u});let d=Math.max(1,parseInt(i)),m=Math.min(100,Math.max(1,parseInt(n))),g=(d-1)*m;p=p.range(g,g+m-1);let{data:h,error:f,count:I}=await p;if(f)throw console.error(`[${t}] Database error:`,f),f;let b=Math.ceil(I/m);return console.log(`[${t}] Suppliers fetched successfully:`,{count:h?.length||0,total:I,page:d,totalPages:b}),r.status(200).json({suppliers:h||[],pagination:{page:d,limit:m,total:I,totalPages:b,hasNextPage:d<b,hasPrevPage:d>1},requestId:t})}catch(e){throw console.error(`[${t}] Error fetching suppliers:`,e),e}}async function c(e,r,t,s){try{let{name:a,contactPerson:i,email:n,phone:o,address:u,website:p,paymentTerms:c,leadTimeDays:d,minimumOrderAmount:m,notes:g}=e.body;if(!a||0===a.trim().length)return r.status(400).json({error:"Validation failed",message:"Supplier name is required",requestId:t});let{data:h}=await l.supabaseAdmin.from("suppliers").select("id").eq("name",a.trim()).single();if(h)return r.status(409).json({error:"Supplier already exists",message:"A supplier with this name already exists",requestId:t});if(n&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(n))return r.status(400).json({error:"Validation failed",message:"Invalid email format",requestId:t});let f={name:a.trim(),contact_person:i?.trim()||null,email:n?.trim()||null,phone:o?.trim()||null,address:u?.trim()||null,website:p?.trim()||null,payment_terms:c?.trim()||"Net 30",lead_time_days:d?parseInt(d):7,minimum_order_amount:m?parseFloat(m):0,notes:g?.trim()||null,is_active:!0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:I,error:b}=await l.supabaseAdmin.from("suppliers").insert([f]).select().single();if(b)throw console.error(`[${t}] Database error creating supplier:`,b),b;return console.log(`[${t}] Supplier created successfully:`,{id:I.id,name:I.name,createdBy:s.id}),r.status(201).json({supplier:I,message:"Supplier created successfully",requestId:t})}catch(e){throw console.error(`[${t}] Error creating supplier:`,e),e}}let d=(0,n.l)(s,"default"),m=(0,n.l)(s,"config"),g=new a.PagesAPIRouteModule({definition:{kind:i.x.PAGES_API,page:"/api/admin/suppliers",pathname:"/api/admin/suppliers",bundlePath:"",filename:""},userland:s})},6482:(e,r,t)=>{t.r(r),t.d(r,{supabaseAdmin:()=>n});var s=t(2885);let a="https://ndlgbcsbidyhxbpqzgqp.supabase.co",i=process.env.SUPABASE_SERVICE_ROLE_KEY;a&&i||console.warn("Missing Supabase environment variables for admin client");let n=(0,s.createClient)(a,i||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",{auth:{autoRefreshToken:!1,persistSession:!1}})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[2805],()=>t(7666));module.exports=s})();