"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/admin/reports/export";
exports.ids = ["pages/api/admin/reports/export"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("bcryptjs");

/***/ }),

/***/ "jsonwebtoken":
/*!*******************************!*\
  !*** external "jsonwebtoken" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("jsonwebtoken");

/***/ }),

/***/ "jspdf":
/*!************************!*\
  !*** external "jspdf" ***!
  \************************/
/***/ ((module) => {

module.exports = require("jspdf");

/***/ }),

/***/ "jspdf-autotable":
/*!**********************************!*\
  !*** external "jspdf-autotable" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("jspdf-autotable");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "speakeasy":
/*!****************************!*\
  !*** external "speakeasy" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("speakeasy");

/***/ }),

/***/ "xlsx":
/*!***********************!*\
  !*** external "xlsx" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("xlsx");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Freports%2Fexport&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Creports%5Cexport.js&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Freports%2Fexport&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Creports%5Cexport.js&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_admin_reports_export_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\admin\\reports\\export.js */ \"(api)/./pages/api/admin/reports/export.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_reports_export_js__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_reports_export_js__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/admin/reports/export\",\n        pathname: \"/api/admin/reports/export\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_admin_reports_export_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Freports%2Fexport&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Creports%5Cexport.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/auth/admin-auth.ts":
/*!********************************!*\
  !*** ./lib/auth/admin-auth.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminLogin: () => (/* binding */ adminLogin),\n/* harmony export */   adminLogout: () => (/* binding */ adminLogout),\n/* harmony export */   authenticateAdminRequest: () => (/* binding */ authenticateAdminRequest),\n/* harmony export */   enableMFA: () => (/* binding */ enableMFA),\n/* harmony export */   generateMFASecret: () => (/* binding */ generateMFASecret),\n/* harmony export */   verifyAdminAuth: () => (/* binding */ verifyAdminAuth),\n/* harmony export */   verifyAdminToken: () => (/* binding */ verifyAdminToken),\n/* harmony export */   verifyMFAAndLogin: () => (/* binding */ verifyMFAAndLogin)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"jsonwebtoken\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _security_audit_logging__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../security/audit-logging */ \"(api)/./lib/security/audit-logging.ts\");\n\n\n\n\n// Handle missing environment variables gracefully\nconst supabaseUrl = \"https://ndlgbcsbidyhxbpqzgqp.supabase.co\" || 0;\nconst supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(supabaseUrl, supabaseKey);\n/**\r\n * Verify admin authentication token\r\n */ async function verifyAdminToken(token) {\n    try {\n        // Handle missing JWT secret gracefully\n        const jwtSecret = process.env.JWT_SECRET || \"placeholder-secret\";\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, jwtSecret);\n        // Get user from database with latest info\n        const { data: user, error } = await supabase.from(\"admin_users\").select(`\r\n        id,\r\n        email,\r\n        role,\r\n        first_name,\r\n        last_name,\r\n        is_active,\r\n        mfa_enabled,\r\n        last_activity,\r\n        permissions\r\n      `).eq(\"id\", decoded.userId).eq(\"is_active\", true).single();\n        if (error || !user) {\n            return {\n                valid: false,\n                error: \"User not found or inactive\"\n            };\n        }\n        // Check if user is still active\n        if (!user.is_active) {\n            return {\n                valid: false,\n                error: \"User account is deactivated\"\n            };\n        }\n        // Update last activity\n        await supabase.from(\"admin_users\").update({\n            last_activity: Date.now()\n        }).eq(\"id\", user.id);\n        return {\n            valid: true,\n            user: {\n                id: user.id,\n                email: user.email,\n                role: user.role,\n                firstName: user.first_name,\n                lastName: user.last_name,\n                isActive: user.is_active,\n                mfaEnabled: user.mfa_enabled,\n                lastActivity: Date.now(),\n                permissions: user.permissions || []\n            }\n        };\n    } catch (error) {\n        return {\n            valid: false,\n            error: \"Invalid token\"\n        };\n    }\n}\n/**\r\n * Admin login with email and password\r\n */ async function adminLogin(email, password, ip) {\n    try {\n        // Check for rate limiting\n        const { data: attempts } = await supabase.from(\"login_attempts\").select(\"*\").eq(\"email\", email).gte(\"created_at\", new Date(Date.now() - 15 * 60 * 1000).toISOString()).order(\"created_at\", {\n            ascending: false\n        });\n        if (attempts && attempts.length >= 5) {\n            await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n                action: \"LOGIN_BLOCKED\",\n                email,\n                ip,\n                reason: \"Too many failed attempts\"\n            });\n            return {\n                success: false,\n                error: \"Account temporarily locked due to too many failed attempts\"\n            };\n        }\n        // Get user from database\n        const { data: user, error } = await supabase.from(\"admin_users\").select(`\r\n        id,\r\n        email,\r\n        password_hash,\r\n        role,\r\n        first_name,\r\n        last_name,\r\n        is_active,\r\n        mfa_enabled,\r\n        mfa_secret,\r\n        permissions\r\n      `).eq(\"email\", email.toLowerCase()).single();\n        if (error || !user) {\n            await recordFailedAttempt(email, ip, error ? `Database error: ${error.message}` : \"User not found\");\n            return {\n                success: false,\n                error: \"Invalid credentials\"\n            };\n        }\n        // Check if user is active\n        if (!user.is_active) {\n            await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n                action: \"LOGIN_DENIED\",\n                userId: user.id,\n                email,\n                ip,\n                reason: \"Account deactivated\"\n            });\n            return {\n                success: false,\n                error: \"Account is deactivated\"\n            };\n        }\n        // Verify password\n        const passwordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, user.password_hash);\n        if (!passwordValid) {\n            await recordFailedAttempt(email, ip, \"Invalid password\");\n            return {\n                success: false,\n                error: \"Invalid credentials\"\n            };\n        }\n        // Clear failed attempts on successful password verification\n        await supabase.from(\"login_attempts\").delete().eq(\"email\", email);\n        // Check if MFA is required\n        if (user.mfa_enabled && user.mfa_secret) {\n            // Return success but indicate MFA is required\n            return {\n                success: true,\n                requiresMFA: true,\n                user: {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    firstName: user.first_name,\n                    lastName: user.last_name,\n                    isActive: user.is_active,\n                    mfaEnabled: user.mfa_enabled,\n                    lastActivity: Date.now(),\n                    permissions: user.permissions || []\n                }\n            };\n        }\n        // Generate JWT token\n        const jwtSecret = process.env.JWT_SECRET || \"placeholder-secret\";\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n            userId: user.id,\n            email: user.email,\n            role: user.role\n        }, jwtSecret, {\n            expiresIn: \"8h\"\n        });\n        // Update last login\n        await supabase.from(\"admin_users\").update({\n            last_login_at: new Date().toISOString(),\n            last_activity: Date.now()\n        }).eq(\"id\", user.id);\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"LOGIN_SUCCESS\",\n            userId: user.id,\n            userRole: user.role,\n            email,\n            ip\n        });\n        return {\n            success: true,\n            token,\n            user: {\n                id: user.id,\n                email: user.email,\n                role: user.role,\n                firstName: user.first_name,\n                lastName: user.last_name,\n                isActive: user.is_active,\n                mfaEnabled: user.mfa_enabled,\n                lastActivity: Date.now(),\n                permissions: user.permissions || []\n            }\n        };\n    } catch (error) {\n        console.error(\"Admin login error:\", error);\n        return {\n            success: false,\n            error: \"Login failed\"\n        };\n    }\n}\n/**\r\n * Verify MFA token and complete login\r\n * Note: This function requires Node.js runtime due to speakeasy dependency\r\n */ async function verifyMFAAndLogin(userId, mfaToken, ip) {\n    // Import speakeasy dynamically to avoid Edge Runtime issues\n    const speakeasy = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! speakeasy */ \"speakeasy\", 23));\n    try {\n        const { data: user, error } = await supabase.from(\"admin_users\").select(`\r\n        id,\r\n        email,\r\n        role,\r\n        first_name,\r\n        last_name,\r\n        is_active,\r\n        mfa_enabled,\r\n        mfa_secret,\r\n        permissions\r\n      `).eq(\"id\", userId).single();\n        if (error || !user || !user.mfa_secret) {\n            return {\n                success: false,\n                error: \"Invalid MFA setup\"\n            };\n        }\n        // Verify MFA token\n        const verified = speakeasy.totp.verify({\n            secret: user.mfa_secret,\n            encoding: \"base32\",\n            token: mfaToken,\n            window: 2\n        });\n        if (!verified) {\n            await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n                action: \"MFA_FAILED\",\n                userId: user.id,\n                email: user.email,\n                ip,\n                reason: \"Invalid MFA token\"\n            });\n            return {\n                success: false,\n                error: \"Invalid MFA token\"\n            };\n        }\n        // Generate JWT token\n        const jwtSecret = process.env.JWT_SECRET || \"placeholder-secret\";\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n            userId: user.id,\n            email: user.email,\n            role: user.role\n        }, jwtSecret, {\n            expiresIn: \"8h\"\n        });\n        // Update last login\n        await supabase.from(\"admin_users\").update({\n            last_login_at: new Date().toISOString(),\n            last_activity: Date.now()\n        }).eq(\"id\", user.id);\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"MFA_LOGIN_SUCCESS\",\n            userId: user.id,\n            userRole: user.role,\n            email: user.email,\n            ip\n        });\n        return {\n            success: true,\n            token,\n            user: {\n                id: user.id,\n                email: user.email,\n                role: user.role,\n                firstName: user.first_name,\n                lastName: user.last_name,\n                isActive: user.is_active,\n                mfaEnabled: user.mfa_enabled,\n                lastActivity: Date.now(),\n                permissions: user.permissions || []\n            }\n        };\n    } catch (error) {\n        console.error(\"MFA verification error:\", error);\n        return {\n            success: false,\n            error: \"MFA verification failed\"\n        };\n    }\n}\n/**\r\n * Generate MFA secret for user\r\n * Note: This function requires Node.js runtime due to speakeasy dependency\r\n */ async function generateMFASecret(userId) {\n    // Import speakeasy dynamically to avoid Edge Runtime issues\n    const speakeasy = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! speakeasy */ \"speakeasy\", 23));\n    try {\n        const { data: user } = await supabase.from(\"admin_users\").select(\"email, first_name, last_name\").eq(\"id\", userId).single();\n        if (!user) return null;\n        const secret = speakeasy.generateSecret({\n            name: `${user.first_name} ${user.last_name}`,\n            issuer: \"Ocean Soul Sparkles Admin\",\n            length: 32\n        });\n        return {\n            secret: secret.base32,\n            qrCode: secret.otpauth_url\n        };\n    } catch (error) {\n        console.error(\"MFA secret generation error:\", error);\n        return null;\n    }\n}\n/**\r\n * Enable MFA for user\r\n * Note: This function requires Node.js runtime due to speakeasy dependency\r\n */ async function enableMFA(userId, secret, token) {\n    // Import speakeasy dynamically to avoid Edge Runtime issues\n    const speakeasy = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! speakeasy */ \"speakeasy\", 23));\n    try {\n        // Verify the token first\n        const verified = speakeasy.totp.verify({\n            secret,\n            encoding: \"base32\",\n            token,\n            window: 2\n        });\n        if (!verified) return false;\n        // Save MFA secret to database\n        const { error } = await supabase.from(\"admin_users\").update({\n            mfa_secret: secret,\n            mfa_enabled: true\n        }).eq(\"id\", userId);\n        if (error) return false;\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"MFA_ENABLED\",\n            userId,\n            reason: \"User enabled MFA\"\n        });\n        return true;\n    } catch (error) {\n        console.error(\"MFA enable error:\", error);\n        return false;\n    }\n}\n/**\r\n * Record failed login attempt\r\n */ async function recordFailedAttempt(email, ip, reason) {\n    await supabase.from(\"login_attempts\").insert({\n        email,\n        ip_address: ip,\n        success: false,\n        reason,\n        created_at: new Date().toISOString()\n    });\n    await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n        action: \"LOGIN_FAILED\",\n        email,\n        ip,\n        reason\n    });\n}\n/**\r\n * Admin logout\r\n */ async function adminLogout(userId, ip) {\n    try {\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"LOGOUT\",\n            userId,\n            ip\n        });\n    } catch (error) {\n        console.error(\"Logout audit error:\", error);\n    }\n}\n/**\r\n * Authenticate admin request (alias for verifyAdminToken for backward compatibility)\r\n */ async function authenticateAdminRequest(token) {\n    return verifyAdminToken(token);\n}\n/**\r\n * Verify admin authentication from NextApiRequest\r\n */ async function verifyAdminAuth(req) {\n    try {\n        // Extract token from headers or cookies\n        const token = req.headers.authorization?.replace(\"Bearer \", \"\") || req.cookies[\"admin-token\"];\n        if (!token) {\n            return {\n                success: false,\n                message: \"No authentication token\"\n            };\n        }\n        const authResult = await verifyAdminToken(token);\n        if (!authResult.valid || !authResult.user) {\n            return {\n                success: false,\n                message: authResult.error || \"Invalid authentication\"\n            };\n        }\n        return {\n            success: true,\n            user: authResult.user\n        };\n    } catch (error) {\n        return {\n            success: false,\n            message: \"Authentication failed\"\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/auth/admin-auth.ts\n");

/***/ }),

/***/ "(api)/./lib/export/csv-generator.js":
/*!*************************************!*\
  !*** ./lib/export/csv-generator.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateCSVReport: () => (/* binding */ generateCSVReport)\n/* harmony export */ });\n/**\n * Generate CSV report from report data\n */ async function generateCSVReport(reportData, dateRange, rangeName) {\n    let csvContent = \"\";\n    // Add header\n    csvContent += `Ocean Soul Sparkles - Business Report\\n`;\n    csvContent += `Report Period: ${formatDateRange(rangeName)}\\n`;\n    csvContent += `Generated: ${new Date().toLocaleDateString(\"en-AU\")}\\n\\n`;\n    // Add overview section\n    csvContent += addOverviewSection(reportData.overview);\n    // Add revenue section\n    csvContent += addRevenueSection(reportData.revenue);\n    // Add bookings section\n    csvContent += addBookingsSection(reportData.bookings);\n    // Add customers section\n    csvContent += addCustomersSection(reportData.customers);\n    return Buffer.from(csvContent, \"utf8\");\n}\n/**\n * Add overview section to CSV\n */ function addOverviewSection(overview) {\n    let content = \"EXECUTIVE SUMMARY\\n\";\n    content += \"Metric,Value,Growth\\n\";\n    content += `Total Revenue,${formatCurrency(overview.totalRevenue)},${overview.revenueGrowth >= 0 ? \"+\" : \"\"}${overview.revenueGrowth.toFixed(1)}%\\n`;\n    content += `Total Bookings,${overview.totalBookings},${overview.bookingGrowth >= 0 ? \"+\" : \"\"}${overview.bookingGrowth.toFixed(1)}%\\n`;\n    content += `Total Customers,${overview.totalCustomers},-\\n`;\n    content += `Average Booking Value,${formatCurrency(overview.averageBookingValue)},-\\n\\n`;\n    return content;\n}\n/**\n * Add revenue section to CSV\n */ function addRevenueSection(revenue) {\n    let content = \"REVENUE ANALYSIS\\n\\n\";\n    // Revenue by service\n    content += \"Revenue by Service\\n\";\n    content += \"Service,Revenue,Percentage\\n\";\n    revenue.byService.forEach((service)=>{\n        content += `\"${service.service}\",${service.amount.toFixed(2)},${service.percentage.toFixed(1)}%\\n`;\n    });\n    content += \"\\n\";\n    // Daily revenue\n    content += \"Daily Revenue\\n\";\n    content += \"Date,Amount\\n\";\n    revenue.daily.forEach((day)=>{\n        const date = new Date(day.date).toLocaleDateString(\"en-AU\");\n        content += `${date},${day.amount.toFixed(2)}\\n`;\n    });\n    content += \"\\n\";\n    return content;\n}\n/**\n * Add bookings section to CSV\n */ function addBookingsSection(bookings) {\n    let content = \"BOOKING ANALYSIS\\n\\n\";\n    // Status breakdown\n    content += \"Booking Status Breakdown\\n\";\n    content += \"Status,Count,Percentage\\n\";\n    bookings.statusBreakdown.forEach((status)=>{\n        content += `${status.status},${status.count},${status.percentage.toFixed(1)}%\\n`;\n    });\n    content += \"\\n\";\n    // Summary statistics\n    content += \"Summary Statistics\\n\";\n    content += \"Metric,Value\\n\";\n    const totalBookings = bookings.statusBreakdown.reduce((sum, status)=>sum + status.count, 0);\n    content += `Total Bookings,${totalBookings}\\n`;\n    content += `Cancellation Rate,${bookings.cancellationRate.toFixed(1)}%\\n`;\n    content += `Success Rate,${(100 - bookings.cancellationRate).toFixed(1)}%\\n\\n`;\n    return content;\n}\n/**\n * Add customers section to CSV\n */ function addCustomersSection(customers) {\n    let content = \"CUSTOMER ANALYSIS\\n\\n\";\n    // Customer metrics\n    content += \"Customer Metrics\\n\";\n    content += \"Metric,Value\\n\";\n    content += `New Customers,${customers.newCustomers}\\n`;\n    content += `Returning Customers,${customers.returningCustomers}\\n`;\n    content += `Customer Lifetime Value,${formatCurrency(customers.customerLifetimeValue)}\\n`;\n    const totalCustomers = customers.newCustomers + customers.returningCustomers;\n    const retentionRate = totalCustomers > 0 ? customers.returningCustomers / totalCustomers * 100 : 0;\n    content += `Retention Rate,${retentionRate.toFixed(1)}%\\n\\n`;\n    // Customer distribution\n    content += \"Customer Distribution\\n\";\n    content += \"Type,Count,Percentage\\n\";\n    const newPercentage = totalCustomers > 0 ? customers.newCustomers / totalCustomers * 100 : 0;\n    const returningPercentage = totalCustomers > 0 ? customers.returningCustomers / totalCustomers * 100 : 0;\n    content += `New Customers,${customers.newCustomers},${newPercentage.toFixed(1)}%\\n`;\n    content += `Returning Customers,${customers.returningCustomers},${returningPercentage.toFixed(1)}%\\n\\n`;\n    return content;\n}\n/**\n * Format currency for display\n */ function formatCurrency(amount) {\n    return new Intl.NumberFormat(\"en-AU\", {\n        style: \"currency\",\n        currency: \"AUD\"\n    }).format(amount);\n}\n/**\n * Format date range for display\n */ function formatDateRange(rangeName) {\n    const ranges = {\n        \"last7days\": \"Last 7 Days\",\n        \"last30days\": \"Last 30 Days\",\n        \"last90days\": \"Last 90 Days\",\n        \"thisyear\": \"This Year\"\n    };\n    return ranges[rangeName] || rangeName;\n}\n/**\n * Escape CSV field if it contains special characters\n */ function escapeCsvField(field) {\n    if (typeof field !== \"string\") {\n        return field;\n    }\n    // If field contains comma, quote, or newline, wrap in quotes and escape quotes\n    if (field.includes(\",\") || field.includes('\"') || field.includes(\"\\n\")) {\n        return `\"${field.replace(/\"/g, '\"\"')}\"`;\n    }\n    return field;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/export/csv-generator.js\n");

/***/ }),

/***/ "(api)/./lib/export/excel-generator.js":
/*!***************************************!*\
  !*** ./lib/export/excel-generator.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateExcelReport: () => (/* binding */ generateExcelReport)\n/* harmony export */ });\nconst XLSX = __webpack_require__(/*! xlsx */ \"xlsx\");\n/**\n * Generate Excel report from report data\n */ async function generateExcelReport(reportData, dateRange, rangeName) {\n    // Create a new workbook\n    const workbook = XLSX.utils.book_new();\n    // Add overview worksheet\n    addOverviewWorksheet(workbook, reportData.overview, rangeName);\n    // Add revenue worksheet\n    addRevenueWorksheet(workbook, reportData.revenue);\n    // Add bookings worksheet\n    addBookingsWorksheet(workbook, reportData.bookings);\n    // Add customers worksheet\n    addCustomersWorksheet(workbook, reportData.customers);\n    // Generate buffer\n    const buffer = XLSX.write(workbook, {\n        type: \"buffer\",\n        bookType: \"xlsx\",\n        compression: true\n    });\n    return buffer;\n}\n/**\n * Add overview worksheet\n */ function addOverviewWorksheet(workbook, overview, rangeName) {\n    const data = [\n        [\n            \"Ocean Soul Sparkles - Business Report\"\n        ],\n        [\n            `Report Period: ${formatDateRange(rangeName)}`\n        ],\n        [\n            `Generated: ${new Date().toLocaleDateString(\"en-AU\")}`\n        ],\n        [],\n        [\n            \"Executive Summary\"\n        ],\n        [],\n        [\n            \"Metric\",\n            \"Value\",\n            \"Growth\"\n        ],\n        [\n            \"Total Revenue\",\n            formatCurrency(overview.totalRevenue),\n            `${overview.revenueGrowth >= 0 ? \"+\" : \"\"}${overview.revenueGrowth.toFixed(1)}%`\n        ],\n        [\n            \"Total Bookings\",\n            overview.totalBookings,\n            `${overview.bookingGrowth >= 0 ? \"+\" : \"\"}${overview.bookingGrowth.toFixed(1)}%`\n        ],\n        [\n            \"Total Customers\",\n            overview.totalCustomers,\n            \"-\"\n        ],\n        [\n            \"Average Booking Value\",\n            formatCurrency(overview.averageBookingValue),\n            \"-\"\n        ]\n    ];\n    const worksheet = XLSX.utils.aoa_to_sheet(data);\n    // Set column widths\n    worksheet[\"!cols\"] = [\n        {\n            width: 25\n        },\n        {\n            width: 20\n        },\n        {\n            width: 15\n        }\n    ];\n    // Style the header\n    if (worksheet[\"A1\"]) {\n        worksheet[\"A1\"].s = {\n            font: {\n                bold: true,\n                sz: 16,\n                color: {\n                    rgb: \"FFFFFF\"\n                }\n            },\n            fill: {\n                fgColor: {\n                    rgb: \"667EEA\"\n                }\n            },\n            alignment: {\n                horizontal: \"center\"\n            }\n        };\n    }\n    // Style the summary header\n    if (worksheet[\"A5\"]) {\n        worksheet[\"A5\"].s = {\n            font: {\n                bold: true,\n                sz: 14\n            },\n            fill: {\n                fgColor: {\n                    rgb: \"F3F4F6\"\n                }\n            }\n        };\n    }\n    // Style the table header\n    [\n        \"A7\",\n        \"B7\",\n        \"C7\"\n    ].forEach((cell)=>{\n        if (worksheet[cell]) {\n            worksheet[cell].s = {\n                font: {\n                    bold: true,\n                    color: {\n                        rgb: \"FFFFFF\"\n                    }\n                },\n                fill: {\n                    fgColor: {\n                        rgb: \"667EEA\"\n                    }\n                },\n                alignment: {\n                    horizontal: \"center\"\n                }\n            };\n        }\n    });\n    XLSX.utils.book_append_sheet(workbook, worksheet, \"Overview\");\n}\n/**\n * Add revenue worksheet\n */ function addRevenueWorksheet(workbook, revenue) {\n    const data = [\n        [\n            \"Revenue Analysis\"\n        ],\n        [],\n        [\n            \"Revenue by Service\"\n        ],\n        [\n            \"Service\",\n            \"Revenue\",\n            \"Percentage\"\n        ],\n        ...revenue.byService.map((service)=>[\n                service.service,\n                service.amount,\n                `${service.percentage.toFixed(1)}%`\n            ]),\n        [],\n        [\n            \"Daily Revenue\"\n        ],\n        [\n            \"Date\",\n            \"Amount\"\n        ],\n        ...revenue.daily.map((day)=>[\n                new Date(day.date).toLocaleDateString(\"en-AU\"),\n                day.amount\n            ])\n    ];\n    const worksheet = XLSX.utils.aoa_to_sheet(data);\n    // Set column widths\n    worksheet[\"!cols\"] = [\n        {\n            width: 25\n        },\n        {\n            width: 15\n        },\n        {\n            width: 15\n        }\n    ];\n    // Style headers\n    if (worksheet[\"A1\"]) {\n        worksheet[\"A1\"].s = {\n            font: {\n                bold: true,\n                sz: 14\n            },\n            fill: {\n                fgColor: {\n                    rgb: \"764BA2\"\n                }\n            },\n            font: {\n                color: {\n                    rgb: \"FFFFFF\"\n                }\n            }\n        };\n    }\n    if (worksheet[\"A3\"]) {\n        worksheet[\"A3\"].s = {\n            font: {\n                bold: true,\n                sz: 12\n            },\n            fill: {\n                fgColor: {\n                    rgb: \"F3F4F6\"\n                }\n            }\n        };\n    }\n    // Style table headers\n    [\n        \"A4\",\n        \"B4\",\n        \"C4\"\n    ].forEach((cell)=>{\n        if (worksheet[cell]) {\n            worksheet[cell].s = {\n                font: {\n                    bold: true,\n                    color: {\n                        rgb: \"FFFFFF\"\n                    }\n                },\n                fill: {\n                    fgColor: {\n                        rgb: \"764BA2\"\n                    }\n                },\n                alignment: {\n                    horizontal: \"center\"\n                }\n            };\n        }\n    });\n    const dailyStartRow = 7 + revenue.byService.length;\n    if (worksheet[`A${dailyStartRow}`]) {\n        worksheet[`A${dailyStartRow}`].s = {\n            font: {\n                bold: true,\n                sz: 12\n            },\n            fill: {\n                fgColor: {\n                    rgb: \"F3F4F6\"\n                }\n            }\n        };\n    }\n    [\n        `A${dailyStartRow + 1}`,\n        `B${dailyStartRow + 1}`\n    ].forEach((cell)=>{\n        if (worksheet[cell]) {\n            worksheet[cell].s = {\n                font: {\n                    bold: true,\n                    color: {\n                        rgb: \"FFFFFF\"\n                    }\n                },\n                fill: {\n                    fgColor: {\n                        rgb: \"764BA2\"\n                    }\n                },\n                alignment: {\n                    horizontal: \"center\"\n                }\n            };\n        }\n    });\n    XLSX.utils.book_append_sheet(workbook, worksheet, \"Revenue\");\n}\n/**\n * Add bookings worksheet\n */ function addBookingsWorksheet(workbook, bookings) {\n    const data = [\n        [\n            \"Booking Analysis\"\n        ],\n        [],\n        [\n            \"Booking Status Breakdown\"\n        ],\n        [\n            \"Status\",\n            \"Count\",\n            \"Percentage\"\n        ],\n        ...bookings.statusBreakdown.map((status)=>[\n                status.status,\n                status.count,\n                `${status.percentage.toFixed(1)}%`\n            ]),\n        [],\n        [\n            \"Summary Statistics\"\n        ],\n        [\n            \"Metric\",\n            \"Value\"\n        ],\n        [\n            \"Total Bookings\",\n            bookings.statusBreakdown.reduce((sum, status)=>sum + status.count, 0)\n        ],\n        [\n            \"Cancellation Rate\",\n            `${bookings.cancellationRate.toFixed(1)}%`\n        ],\n        [\n            \"Success Rate\",\n            `${(100 - bookings.cancellationRate).toFixed(1)}%`\n        ]\n    ];\n    const worksheet = XLSX.utils.aoa_to_sheet(data);\n    // Set column widths\n    worksheet[\"!cols\"] = [\n        {\n            width: 20\n        },\n        {\n            width: 15\n        },\n        {\n            width: 15\n        }\n    ];\n    // Style headers\n    if (worksheet[\"A1\"]) {\n        worksheet[\"A1\"].s = {\n            font: {\n                bold: true,\n                sz: 14,\n                color: {\n                    rgb: \"FFFFFF\"\n                }\n            },\n            fill: {\n                fgColor: {\n                    rgb: \"22C55E\"\n                }\n            }\n        };\n    }\n    if (worksheet[\"A3\"]) {\n        worksheet[\"A3\"].s = {\n            font: {\n                bold: true,\n                sz: 12\n            },\n            fill: {\n                fgColor: {\n                    rgb: \"F3F4F6\"\n                }\n            }\n        };\n    }\n    // Style table headers\n    [\n        \"A4\",\n        \"B4\",\n        \"C4\"\n    ].forEach((cell)=>{\n        if (worksheet[cell]) {\n            worksheet[cell].s = {\n                font: {\n                    bold: true,\n                    color: {\n                        rgb: \"FFFFFF\"\n                    }\n                },\n                fill: {\n                    fgColor: {\n                        rgb: \"22C55E\"\n                    }\n                },\n                alignment: {\n                    horizontal: \"center\"\n                }\n            };\n        }\n    });\n    const summaryStartRow = 7 + bookings.statusBreakdown.length;\n    if (worksheet[`A${summaryStartRow}`]) {\n        worksheet[`A${summaryStartRow}`].s = {\n            font: {\n                bold: true,\n                sz: 12\n            },\n            fill: {\n                fgColor: {\n                    rgb: \"F3F4F6\"\n                }\n            }\n        };\n    }\n    [\n        `A${summaryStartRow + 1}`,\n        `B${summaryStartRow + 1}`\n    ].forEach((cell)=>{\n        if (worksheet[cell]) {\n            worksheet[cell].s = {\n                font: {\n                    bold: true,\n                    color: {\n                        rgb: \"FFFFFF\"\n                    }\n                },\n                fill: {\n                    fgColor: {\n                        rgb: \"22C55E\"\n                    }\n                },\n                alignment: {\n                    horizontal: \"center\"\n                }\n            };\n        }\n    });\n    XLSX.utils.book_append_sheet(workbook, worksheet, \"Bookings\");\n}\n/**\n * Add customers worksheet\n */ function addCustomersWorksheet(workbook, customers) {\n    const data = [\n        [\n            \"Customer Analysis\"\n        ],\n        [],\n        [\n            \"Customer Metrics\"\n        ],\n        [\n            \"Metric\",\n            \"Value\"\n        ],\n        [\n            \"New Customers\",\n            customers.newCustomers\n        ],\n        [\n            \"Returning Customers\",\n            customers.returningCustomers\n        ],\n        [\n            \"Customer Lifetime Value\",\n            formatCurrency(customers.customerLifetimeValue)\n        ],\n        [\n            \"Retention Rate\",\n            `${(customers.returningCustomers / (customers.newCustomers + customers.returningCustomers) * 100).toFixed(1)}%`\n        ],\n        [],\n        [\n            \"Customer Distribution\"\n        ],\n        [\n            \"Type\",\n            \"Count\",\n            \"Percentage\"\n        ],\n        [\n            \"New Customers\",\n            customers.newCustomers,\n            `${(customers.newCustomers / (customers.newCustomers + customers.returningCustomers) * 100).toFixed(1)}%`\n        ],\n        [\n            \"Returning Customers\",\n            customers.returningCustomers,\n            `${(customers.returningCustomers / (customers.newCustomers + customers.returningCustomers) * 100).toFixed(1)}%`\n        ]\n    ];\n    const worksheet = XLSX.utils.aoa_to_sheet(data);\n    // Set column widths\n    worksheet[\"!cols\"] = [\n        {\n            width: 25\n        },\n        {\n            width: 15\n        },\n        {\n            width: 15\n        }\n    ];\n    // Style headers\n    if (worksheet[\"A1\"]) {\n        worksheet[\"A1\"].s = {\n            font: {\n                bold: true,\n                sz: 14,\n                color: {\n                    rgb: \"FFFFFF\"\n                }\n            },\n            fill: {\n                fgColor: {\n                    rgb: \"3B82F6\"\n                }\n            }\n        };\n    }\n    if (worksheet[\"A3\"]) {\n        worksheet[\"A3\"].s = {\n            font: {\n                bold: true,\n                sz: 12\n            },\n            fill: {\n                fgColor: {\n                    rgb: \"F3F4F6\"\n                }\n            }\n        };\n    }\n    if (worksheet[\"A10\"]) {\n        worksheet[\"A10\"].s = {\n            font: {\n                bold: true,\n                sz: 12\n            },\n            fill: {\n                fgColor: {\n                    rgb: \"F3F4F6\"\n                }\n            }\n        };\n    }\n    // Style table headers\n    [\n        \"A4\",\n        \"B4\"\n    ].forEach((cell)=>{\n        if (worksheet[cell]) {\n            worksheet[cell].s = {\n                font: {\n                    bold: true,\n                    color: {\n                        rgb: \"FFFFFF\"\n                    }\n                },\n                fill: {\n                    fgColor: {\n                        rgb: \"3B82F6\"\n                    }\n                },\n                alignment: {\n                    horizontal: \"center\"\n                }\n            };\n        }\n    });\n    [\n        \"A11\",\n        \"B11\",\n        \"C11\"\n    ].forEach((cell)=>{\n        if (worksheet[cell]) {\n            worksheet[cell].s = {\n                font: {\n                    bold: true,\n                    color: {\n                        rgb: \"FFFFFF\"\n                    }\n                },\n                fill: {\n                    fgColor: {\n                        rgb: \"3B82F6\"\n                    }\n                },\n                alignment: {\n                    horizontal: \"center\"\n                }\n            };\n        }\n    });\n    XLSX.utils.book_append_sheet(workbook, worksheet, \"Customers\");\n}\n/**\n * Format currency for display\n */ function formatCurrency(amount) {\n    return new Intl.NumberFormat(\"en-AU\", {\n        style: \"currency\",\n        currency: \"AUD\"\n    }).format(amount);\n}\n/**\n * Format date range for display\n */ function formatDateRange(rangeName) {\n    const ranges = {\n        \"last7days\": \"Last 7 Days\",\n        \"last30days\": \"Last 30 Days\",\n        \"last90days\": \"Last 90 Days\",\n        \"thisyear\": \"This Year\"\n    };\n    return ranges[rangeName] || rangeName;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/export/excel-generator.js\n");

/***/ }),

/***/ "(api)/./lib/export/pdf-generator.js":
/*!*************************************!*\
  !*** ./lib/export/pdf-generator.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generatePDFReport: () => (/* binding */ generatePDFReport)\n/* harmony export */ });\nconst jsPDF = (__webpack_require__(/*! jspdf */ \"jspdf\").jsPDF);\n__webpack_require__(/*! jspdf-autotable */ \"jspdf-autotable\");\n/**\n * Generate PDF report from report data\n */ async function generatePDFReport(reportData, dateRange, rangeName) {\n    const doc = new jsPDF();\n    // Set up document properties\n    doc.setProperties({\n        title: \"Ocean Soul Sparkles - Business Report\",\n        subject: `Business Analytics Report - ${rangeName}`,\n        author: \"Ocean Soul Sparkles Admin System\",\n        creator: \"Ocean Soul Sparkles Admin Dashboard\"\n    });\n    // Add header\n    addHeader(doc, rangeName, dateRange);\n    let yPosition = 60;\n    // Add overview section\n    yPosition = addOverviewSection(doc, reportData.overview, yPosition);\n    // Add revenue section\n    if (yPosition > 200) {\n        doc.addPage();\n        yPosition = 20;\n    }\n    yPosition = addRevenueSection(doc, reportData.revenue, yPosition);\n    // Add bookings section\n    if (yPosition > 200) {\n        doc.addPage();\n        yPosition = 20;\n    }\n    yPosition = addBookingsSection(doc, reportData.bookings, yPosition);\n    // Add customers section\n    if (yPosition > 200) {\n        doc.addPage();\n        yPosition = 20;\n    }\n    yPosition = addCustomersSection(doc, reportData.customers, yPosition);\n    // Add footer\n    addFooter(doc, reportData.metadata);\n    return doc.output(\"arraybuffer\");\n}\n/**\n * Add document header\n */ function addHeader(doc, rangeName, dateRange) {\n    // Company logo area (placeholder)\n    doc.setFillColor(102, 126, 234);\n    doc.rect(20, 10, 170, 30, \"F\");\n    // Company name\n    doc.setTextColor(255, 255, 255);\n    doc.setFontSize(20);\n    doc.setFont(\"helvetica\", \"bold\");\n    doc.text(\"Ocean Soul Sparkles\", 25, 25);\n    // Report title\n    doc.setFontSize(14);\n    doc.setFont(\"helvetica\", \"normal\");\n    doc.text(\"Business Analytics Report\", 25, 35);\n    // Date range\n    doc.setTextColor(0, 0, 0);\n    doc.setFontSize(12);\n    doc.text(`Report Period: ${formatDateRange(rangeName)}`, 20, 50);\n    doc.text(`Generated: ${new Date().toLocaleDateString(\"en-AU\")}`, 120, 50);\n}\n/**\n * Add overview metrics section\n */ function addOverviewSection(doc, overview, yPosition) {\n    doc.setFontSize(16);\n    doc.setFont(\"helvetica\", \"bold\");\n    doc.setTextColor(102, 126, 234);\n    doc.text(\"Executive Summary\", 20, yPosition);\n    yPosition += 15;\n    // Create overview table\n    const overviewData = [\n        [\n            \"Total Revenue\",\n            formatCurrency(overview.totalRevenue),\n            `${overview.revenueGrowth >= 0 ? \"+\" : \"\"}${overview.revenueGrowth.toFixed(1)}%`\n        ],\n        [\n            \"Total Bookings\",\n            overview.totalBookings.toString(),\n            `${overview.bookingGrowth >= 0 ? \"+\" : \"\"}${overview.bookingGrowth.toFixed(1)}%`\n        ],\n        [\n            \"Total Customers\",\n            overview.totalCustomers.toString(),\n            \"-\"\n        ],\n        [\n            \"Average Booking Value\",\n            formatCurrency(overview.averageBookingValue),\n            \"-\"\n        ]\n    ];\n    doc.autoTable({\n        startY: yPosition,\n        head: [\n            [\n                \"Metric\",\n                \"Value\",\n                \"Growth\"\n            ]\n        ],\n        body: overviewData,\n        theme: \"grid\",\n        headStyles: {\n            fillColor: [\n                102,\n                126,\n                234\n            ],\n            textColor: [\n                255,\n                255,\n                255\n            ],\n            fontStyle: \"bold\"\n        },\n        styles: {\n            fontSize: 10,\n            cellPadding: 5\n        },\n        columnStyles: {\n            0: {\n                cellWidth: 60\n            },\n            1: {\n                cellWidth: 50,\n                halign: \"right\"\n            },\n            2: {\n                cellWidth: 30,\n                halign: \"center\"\n            }\n        }\n    });\n    return doc.lastAutoTable.finalY + 20;\n}\n/**\n * Add revenue analysis section\n */ function addRevenueSection(doc, revenue, yPosition) {\n    doc.setFontSize(16);\n    doc.setFont(\"helvetica\", \"bold\");\n    doc.setTextColor(102, 126, 234);\n    doc.text(\"Revenue Analysis\", 20, yPosition);\n    yPosition += 15;\n    // Revenue by service table\n    if (revenue.byService && revenue.byService.length > 0) {\n        doc.setFontSize(12);\n        doc.setFont(\"helvetica\", \"bold\");\n        doc.setTextColor(0, 0, 0);\n        doc.text(\"Revenue by Service\", 20, yPosition);\n        yPosition += 10;\n        const serviceData = revenue.byService.map((service)=>[\n                service.service,\n                formatCurrency(service.amount),\n                `${service.percentage.toFixed(1)}%`\n            ]);\n        doc.autoTable({\n            startY: yPosition,\n            head: [\n                [\n                    \"Service\",\n                    \"Revenue\",\n                    \"Percentage\"\n                ]\n            ],\n            body: serviceData,\n            theme: \"striped\",\n            headStyles: {\n                fillColor: [\n                    118,\n                    75,\n                    162\n                ],\n                textColor: [\n                    255,\n                    255,\n                    255\n                ],\n                fontStyle: \"bold\"\n            },\n            styles: {\n                fontSize: 9,\n                cellPadding: 4\n            },\n            columnStyles: {\n                0: {\n                    cellWidth: 80\n                },\n                1: {\n                    cellWidth: 40,\n                    halign: \"right\"\n                },\n                2: {\n                    cellWidth: 30,\n                    halign: \"center\"\n                }\n            }\n        });\n        yPosition = doc.lastAutoTable.finalY + 15;\n    }\n    return yPosition;\n}\n/**\n * Add bookings analysis section\n */ function addBookingsSection(doc, bookings, yPosition) {\n    doc.setFontSize(16);\n    doc.setFont(\"helvetica\", \"bold\");\n    doc.setTextColor(102, 126, 234);\n    doc.text(\"Booking Analysis\", 20, yPosition);\n    yPosition += 15;\n    // Booking status breakdown\n    if (bookings.statusBreakdown && bookings.statusBreakdown.length > 0) {\n        doc.setFontSize(12);\n        doc.setFont(\"helvetica\", \"bold\");\n        doc.setTextColor(0, 0, 0);\n        doc.text(\"Booking Status Breakdown\", 20, yPosition);\n        yPosition += 10;\n        const statusData = bookings.statusBreakdown.map((status)=>[\n                status.status,\n                status.count.toString(),\n                `${status.percentage.toFixed(1)}%`\n            ]);\n        doc.autoTable({\n            startY: yPosition,\n            head: [\n                [\n                    \"Status\",\n                    \"Count\",\n                    \"Percentage\"\n                ]\n            ],\n            body: statusData,\n            theme: \"striped\",\n            headStyles: {\n                fillColor: [\n                    34,\n                    197,\n                    94\n                ],\n                textColor: [\n                    255,\n                    255,\n                    255\n                ],\n                fontStyle: \"bold\"\n            },\n            styles: {\n                fontSize: 9,\n                cellPadding: 4\n            },\n            columnStyles: {\n                0: {\n                    cellWidth: 60\n                },\n                1: {\n                    cellWidth: 30,\n                    halign: \"center\"\n                },\n                2: {\n                    cellWidth: 30,\n                    halign: \"center\"\n                }\n            }\n        });\n        yPosition = doc.lastAutoTable.finalY + 10;\n    }\n    // Cancellation rate\n    doc.setFontSize(10);\n    doc.setFont(\"helvetica\", \"normal\");\n    doc.text(`Cancellation Rate: ${bookings.cancellationRate.toFixed(1)}%`, 20, yPosition);\n    return yPosition + 15;\n}\n/**\n * Add customers analysis section\n */ function addCustomersSection(doc, customers, yPosition) {\n    doc.setFontSize(16);\n    doc.setFont(\"helvetica\", \"bold\");\n    doc.setTextColor(102, 126, 234);\n    doc.text(\"Customer Analysis\", 20, yPosition);\n    yPosition += 15;\n    // Customer metrics table\n    const customerData = [\n        [\n            \"New Customers\",\n            customers.newCustomers.toString()\n        ],\n        [\n            \"Returning Customers\",\n            customers.returningCustomers.toString()\n        ],\n        [\n            \"Customer Lifetime Value\",\n            formatCurrency(customers.customerLifetimeValue)\n        ]\n    ];\n    doc.autoTable({\n        startY: yPosition,\n        head: [\n            [\n                \"Metric\",\n                \"Value\"\n            ]\n        ],\n        body: customerData,\n        theme: \"grid\",\n        headStyles: {\n            fillColor: [\n                59,\n                130,\n                246\n            ],\n            textColor: [\n                255,\n                255,\n                255\n            ],\n            fontStyle: \"bold\"\n        },\n        styles: {\n            fontSize: 10,\n            cellPadding: 5\n        },\n        columnStyles: {\n            0: {\n                cellWidth: 80\n            },\n            1: {\n                cellWidth: 50,\n                halign: \"right\"\n            }\n        }\n    });\n    return doc.lastAutoTable.finalY + 20;\n}\n/**\n * Add document footer\n */ function addFooter(doc, metadata) {\n    const pageCount = doc.internal.getNumberOfPages();\n    for(let i = 1; i <= pageCount; i++){\n        doc.setPage(i);\n        // Footer line\n        doc.setDrawColor(200, 200, 200);\n        doc.line(20, 280, 190, 280);\n        // Footer text\n        doc.setFontSize(8);\n        doc.setFont(\"helvetica\", \"normal\");\n        doc.setTextColor(100, 100, 100);\n        doc.text(\"Ocean Soul Sparkles - Confidential Business Report\", 20, 285);\n        doc.text(`Page ${i} of ${pageCount}`, 160, 285);\n        doc.text(`Generated: ${new Date(metadata.generatedAt).toLocaleString(\"en-AU\")}`, 20, 290);\n    }\n}\n/**\n * Format currency for display\n */ function formatCurrency(amount) {\n    return new Intl.NumberFormat(\"en-AU\", {\n        style: \"currency\",\n        currency: \"AUD\"\n    }).format(amount);\n}\n/**\n * Format date range for display\n */ function formatDateRange(rangeName) {\n    const ranges = {\n        \"last7days\": \"Last 7 Days\",\n        \"last30days\": \"Last 30 Days\",\n        \"last90days\": \"Last 90 Days\",\n        \"thisyear\": \"This Year\"\n    };\n    return ranges[rangeName] || rangeName;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWIvZXhwb3J0L3BkZi1nZW5lcmF0b3IuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLE1BQU1BLFFBQVFDLGlEQUFzQjtBQUNwQ0EsbUJBQU9BLENBQUM7QUFFUjs7Q0FFQyxHQUNNLGVBQWVDLGtCQUFrQkMsVUFBVSxFQUFFQyxTQUFTLEVBQUVDLFNBQVM7SUFDdEUsTUFBTUMsTUFBTSxJQUFJTjtJQUVoQiw2QkFBNkI7SUFDN0JNLElBQUlDLGFBQWEsQ0FBQztRQUNoQkMsT0FBTztRQUNQQyxTQUFTLENBQUMsNEJBQTRCLEVBQUVKLFVBQVUsQ0FBQztRQUNuREssUUFBUTtRQUNSQyxTQUFTO0lBQ1g7SUFFQSxhQUFhO0lBQ2JDLFVBQVVOLEtBQUtELFdBQVdEO0lBRTFCLElBQUlTLFlBQVk7SUFFaEIsdUJBQXVCO0lBQ3ZCQSxZQUFZQyxtQkFBbUJSLEtBQUtILFdBQVdZLFFBQVEsRUFBRUY7SUFFekQsc0JBQXNCO0lBQ3RCLElBQUlBLFlBQVksS0FBSztRQUNuQlAsSUFBSVUsT0FBTztRQUNYSCxZQUFZO0lBQ2Q7SUFDQUEsWUFBWUksa0JBQWtCWCxLQUFLSCxXQUFXZSxPQUFPLEVBQUVMO0lBRXZELHVCQUF1QjtJQUN2QixJQUFJQSxZQUFZLEtBQUs7UUFDbkJQLElBQUlVLE9BQU87UUFDWEgsWUFBWTtJQUNkO0lBQ0FBLFlBQVlNLG1CQUFtQmIsS0FBS0gsV0FBV2lCLFFBQVEsRUFBRVA7SUFFekQsd0JBQXdCO0lBQ3hCLElBQUlBLFlBQVksS0FBSztRQUNuQlAsSUFBSVUsT0FBTztRQUNYSCxZQUFZO0lBQ2Q7SUFDQUEsWUFBWVEsb0JBQW9CZixLQUFLSCxXQUFXbUIsU0FBUyxFQUFFVDtJQUUzRCxhQUFhO0lBQ2JVLFVBQVVqQixLQUFLSCxXQUFXcUIsUUFBUTtJQUVsQyxPQUFPbEIsSUFBSW1CLE1BQU0sQ0FBQztBQUNwQjtBQUVBOztDQUVDLEdBQ0QsU0FBU2IsVUFBVU4sR0FBRyxFQUFFRCxTQUFTLEVBQUVELFNBQVM7SUFDMUMsa0NBQWtDO0lBQ2xDRSxJQUFJb0IsWUFBWSxDQUFDLEtBQUssS0FBSztJQUMzQnBCLElBQUlxQixJQUFJLENBQUMsSUFBSSxJQUFJLEtBQUssSUFBSTtJQUUxQixlQUFlO0lBQ2ZyQixJQUFJc0IsWUFBWSxDQUFDLEtBQUssS0FBSztJQUMzQnRCLElBQUl1QixXQUFXLENBQUM7SUFDaEJ2QixJQUFJd0IsT0FBTyxDQUFDLGFBQWE7SUFDekJ4QixJQUFJeUIsSUFBSSxDQUFDLHVCQUF1QixJQUFJO0lBRXBDLGVBQWU7SUFDZnpCLElBQUl1QixXQUFXLENBQUM7SUFDaEJ2QixJQUFJd0IsT0FBTyxDQUFDLGFBQWE7SUFDekJ4QixJQUFJeUIsSUFBSSxDQUFDLDZCQUE2QixJQUFJO0lBRTFDLGFBQWE7SUFDYnpCLElBQUlzQixZQUFZLENBQUMsR0FBRyxHQUFHO0lBQ3ZCdEIsSUFBSXVCLFdBQVcsQ0FBQztJQUNoQnZCLElBQUl5QixJQUFJLENBQUMsQ0FBQyxlQUFlLEVBQUVDLGdCQUFnQjNCLFdBQVcsQ0FBQyxFQUFFLElBQUk7SUFDN0RDLElBQUl5QixJQUFJLENBQUMsQ0FBQyxXQUFXLEVBQUUsSUFBSUUsT0FBT0Msa0JBQWtCLENBQUMsU0FBUyxDQUFDLEVBQUUsS0FBSztBQUN4RTtBQUVBOztDQUVDLEdBQ0QsU0FBU3BCLG1CQUFtQlIsR0FBRyxFQUFFUyxRQUFRLEVBQUVGLFNBQVM7SUFDbERQLElBQUl1QixXQUFXLENBQUM7SUFDaEJ2QixJQUFJd0IsT0FBTyxDQUFDLGFBQWE7SUFDekJ4QixJQUFJc0IsWUFBWSxDQUFDLEtBQUssS0FBSztJQUMzQnRCLElBQUl5QixJQUFJLENBQUMscUJBQXFCLElBQUlsQjtJQUVsQ0EsYUFBYTtJQUViLHdCQUF3QjtJQUN4QixNQUFNc0IsZUFBZTtRQUNuQjtZQUFDO1lBQWlCQyxlQUFlckIsU0FBU3NCLFlBQVk7WUFBRyxDQUFDLEVBQUV0QixTQUFTdUIsYUFBYSxJQUFJLElBQUksTUFBTSxHQUFHLEVBQUV2QixTQUFTdUIsYUFBYSxDQUFDQyxPQUFPLENBQUMsR0FBRyxDQUFDLENBQUM7U0FBQztRQUMxSTtZQUFDO1lBQWtCeEIsU0FBU3lCLGFBQWEsQ0FBQ0MsUUFBUTtZQUFJLENBQUMsRUFBRTFCLFNBQVMyQixhQUFhLElBQUksSUFBSSxNQUFNLEdBQUcsRUFBRTNCLFNBQVMyQixhQUFhLENBQUNILE9BQU8sQ0FBQyxHQUFHLENBQUMsQ0FBQztTQUFDO1FBQ3ZJO1lBQUM7WUFBbUJ4QixTQUFTNEIsY0FBYyxDQUFDRixRQUFRO1lBQUk7U0FBSTtRQUM1RDtZQUFDO1lBQXlCTCxlQUFlckIsU0FBUzZCLG1CQUFtQjtZQUFHO1NBQUk7S0FDN0U7SUFFRHRDLElBQUl1QyxTQUFTLENBQUM7UUFDWkMsUUFBUWpDO1FBQ1JrQyxNQUFNO1lBQUM7Z0JBQUM7Z0JBQVU7Z0JBQVM7YUFBUztTQUFDO1FBQ3JDQyxNQUFNYjtRQUNOYyxPQUFPO1FBQ1BDLFlBQVk7WUFDVkMsV0FBVztnQkFBQztnQkFBSztnQkFBSzthQUFJO1lBQzFCQyxXQUFXO2dCQUFDO2dCQUFLO2dCQUFLO2FBQUk7WUFDMUJDLFdBQVc7UUFDYjtRQUNBQyxRQUFRO1lBQ05DLFVBQVU7WUFDVkMsYUFBYTtRQUNmO1FBQ0FDLGNBQWM7WUFDWixHQUFHO2dCQUFFQyxXQUFXO1lBQUc7WUFDbkIsR0FBRztnQkFBRUEsV0FBVztnQkFBSUMsUUFBUTtZQUFRO1lBQ3BDLEdBQUc7Z0JBQUVELFdBQVc7Z0JBQUlDLFFBQVE7WUFBUztRQUN2QztJQUNGO0lBRUEsT0FBT3JELElBQUlzRCxhQUFhLENBQUNDLE1BQU0sR0FBRztBQUNwQztBQUVBOztDQUVDLEdBQ0QsU0FBUzVDLGtCQUFrQlgsR0FBRyxFQUFFWSxPQUFPLEVBQUVMLFNBQVM7SUFDaERQLElBQUl1QixXQUFXLENBQUM7SUFDaEJ2QixJQUFJd0IsT0FBTyxDQUFDLGFBQWE7SUFDekJ4QixJQUFJc0IsWUFBWSxDQUFDLEtBQUssS0FBSztJQUMzQnRCLElBQUl5QixJQUFJLENBQUMsb0JBQW9CLElBQUlsQjtJQUVqQ0EsYUFBYTtJQUViLDJCQUEyQjtJQUMzQixJQUFJSyxRQUFRNEMsU0FBUyxJQUFJNUMsUUFBUTRDLFNBQVMsQ0FBQ0MsTUFBTSxHQUFHLEdBQUc7UUFDckR6RCxJQUFJdUIsV0FBVyxDQUFDO1FBQ2hCdkIsSUFBSXdCLE9BQU8sQ0FBQyxhQUFhO1FBQ3pCeEIsSUFBSXNCLFlBQVksQ0FBQyxHQUFHLEdBQUc7UUFDdkJ0QixJQUFJeUIsSUFBSSxDQUFDLHNCQUFzQixJQUFJbEI7UUFDbkNBLGFBQWE7UUFFYixNQUFNbUQsY0FBYzlDLFFBQVE0QyxTQUFTLENBQUNHLEdBQUcsQ0FBQ0MsQ0FBQUEsVUFBVztnQkFDbkRBLFFBQVFBLE9BQU87Z0JBQ2Y5QixlQUFlOEIsUUFBUUMsTUFBTTtnQkFDN0IsQ0FBQyxFQUFFRCxRQUFRRSxVQUFVLENBQUM3QixPQUFPLENBQUMsR0FBRyxDQUFDLENBQUM7YUFDcEM7UUFFRGpDLElBQUl1QyxTQUFTLENBQUM7WUFDWkMsUUFBUWpDO1lBQ1JrQyxNQUFNO2dCQUFDO29CQUFDO29CQUFXO29CQUFXO2lCQUFhO2FBQUM7WUFDNUNDLE1BQU1nQjtZQUNOZixPQUFPO1lBQ1BDLFlBQVk7Z0JBQ1ZDLFdBQVc7b0JBQUM7b0JBQUs7b0JBQUk7aUJBQUk7Z0JBQ3pCQyxXQUFXO29CQUFDO29CQUFLO29CQUFLO2lCQUFJO2dCQUMxQkMsV0FBVztZQUNiO1lBQ0FDLFFBQVE7Z0JBQ05DLFVBQVU7Z0JBQ1ZDLGFBQWE7WUFDZjtZQUNBQyxjQUFjO2dCQUNaLEdBQUc7b0JBQUVDLFdBQVc7Z0JBQUc7Z0JBQ25CLEdBQUc7b0JBQUVBLFdBQVc7b0JBQUlDLFFBQVE7Z0JBQVE7Z0JBQ3BDLEdBQUc7b0JBQUVELFdBQVc7b0JBQUlDLFFBQVE7Z0JBQVM7WUFDdkM7UUFDRjtRQUVBOUMsWUFBWVAsSUFBSXNELGFBQWEsQ0FBQ0MsTUFBTSxHQUFHO0lBQ3pDO0lBRUEsT0FBT2hEO0FBQ1Q7QUFFQTs7Q0FFQyxHQUNELFNBQVNNLG1CQUFtQmIsR0FBRyxFQUFFYyxRQUFRLEVBQUVQLFNBQVM7SUFDbERQLElBQUl1QixXQUFXLENBQUM7SUFDaEJ2QixJQUFJd0IsT0FBTyxDQUFDLGFBQWE7SUFDekJ4QixJQUFJc0IsWUFBWSxDQUFDLEtBQUssS0FBSztJQUMzQnRCLElBQUl5QixJQUFJLENBQUMsb0JBQW9CLElBQUlsQjtJQUVqQ0EsYUFBYTtJQUViLDJCQUEyQjtJQUMzQixJQUFJTyxTQUFTaUQsZUFBZSxJQUFJakQsU0FBU2lELGVBQWUsQ0FBQ04sTUFBTSxHQUFHLEdBQUc7UUFDbkV6RCxJQUFJdUIsV0FBVyxDQUFDO1FBQ2hCdkIsSUFBSXdCLE9BQU8sQ0FBQyxhQUFhO1FBQ3pCeEIsSUFBSXNCLFlBQVksQ0FBQyxHQUFHLEdBQUc7UUFDdkJ0QixJQUFJeUIsSUFBSSxDQUFDLDRCQUE0QixJQUFJbEI7UUFDekNBLGFBQWE7UUFFYixNQUFNeUQsYUFBYWxELFNBQVNpRCxlQUFlLENBQUNKLEdBQUcsQ0FBQ00sQ0FBQUEsU0FBVTtnQkFDeERBLE9BQU9BLE1BQU07Z0JBQ2JBLE9BQU9DLEtBQUssQ0FBQy9CLFFBQVE7Z0JBQ3JCLENBQUMsRUFBRThCLE9BQU9ILFVBQVUsQ0FBQzdCLE9BQU8sQ0FBQyxHQUFHLENBQUMsQ0FBQzthQUNuQztRQUVEakMsSUFBSXVDLFNBQVMsQ0FBQztZQUNaQyxRQUFRakM7WUFDUmtDLE1BQU07Z0JBQUM7b0JBQUM7b0JBQVU7b0JBQVM7aUJBQWE7YUFBQztZQUN6Q0MsTUFBTXNCO1lBQ05yQixPQUFPO1lBQ1BDLFlBQVk7Z0JBQ1ZDLFdBQVc7b0JBQUM7b0JBQUk7b0JBQUs7aUJBQUc7Z0JBQ3hCQyxXQUFXO29CQUFDO29CQUFLO29CQUFLO2lCQUFJO2dCQUMxQkMsV0FBVztZQUNiO1lBQ0FDLFFBQVE7Z0JBQ05DLFVBQVU7Z0JBQ1ZDLGFBQWE7WUFDZjtZQUNBQyxjQUFjO2dCQUNaLEdBQUc7b0JBQUVDLFdBQVc7Z0JBQUc7Z0JBQ25CLEdBQUc7b0JBQUVBLFdBQVc7b0JBQUlDLFFBQVE7Z0JBQVM7Z0JBQ3JDLEdBQUc7b0JBQUVELFdBQVc7b0JBQUlDLFFBQVE7Z0JBQVM7WUFDdkM7UUFDRjtRQUVBOUMsWUFBWVAsSUFBSXNELGFBQWEsQ0FBQ0MsTUFBTSxHQUFHO0lBQ3pDO0lBRUEsb0JBQW9CO0lBQ3BCdkQsSUFBSXVCLFdBQVcsQ0FBQztJQUNoQnZCLElBQUl3QixPQUFPLENBQUMsYUFBYTtJQUN6QnhCLElBQUl5QixJQUFJLENBQUMsQ0FBQyxtQkFBbUIsRUFBRVgsU0FBU3FELGdCQUFnQixDQUFDbEMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUUsSUFBSTFCO0lBRTVFLE9BQU9BLFlBQVk7QUFDckI7QUFFQTs7Q0FFQyxHQUNELFNBQVNRLG9CQUFvQmYsR0FBRyxFQUFFZ0IsU0FBUyxFQUFFVCxTQUFTO0lBQ3BEUCxJQUFJdUIsV0FBVyxDQUFDO0lBQ2hCdkIsSUFBSXdCLE9BQU8sQ0FBQyxhQUFhO0lBQ3pCeEIsSUFBSXNCLFlBQVksQ0FBQyxLQUFLLEtBQUs7SUFDM0J0QixJQUFJeUIsSUFBSSxDQUFDLHFCQUFxQixJQUFJbEI7SUFFbENBLGFBQWE7SUFFYix5QkFBeUI7SUFDekIsTUFBTTZELGVBQWU7UUFDbkI7WUFBQztZQUFpQnBELFVBQVVxRCxZQUFZLENBQUNsQyxRQUFRO1NBQUc7UUFDcEQ7WUFBQztZQUF1Qm5CLFVBQVVzRCxrQkFBa0IsQ0FBQ25DLFFBQVE7U0FBRztRQUNoRTtZQUFDO1lBQTJCTCxlQUFlZCxVQUFVdUQscUJBQXFCO1NBQUU7S0FDN0U7SUFFRHZFLElBQUl1QyxTQUFTLENBQUM7UUFDWkMsUUFBUWpDO1FBQ1JrQyxNQUFNO1lBQUM7Z0JBQUM7Z0JBQVU7YUFBUTtTQUFDO1FBQzNCQyxNQUFNMEI7UUFDTnpCLE9BQU87UUFDUEMsWUFBWTtZQUNWQyxXQUFXO2dCQUFDO2dCQUFJO2dCQUFLO2FBQUk7WUFDekJDLFdBQVc7Z0JBQUM7Z0JBQUs7Z0JBQUs7YUFBSTtZQUMxQkMsV0FBVztRQUNiO1FBQ0FDLFFBQVE7WUFDTkMsVUFBVTtZQUNWQyxhQUFhO1FBQ2Y7UUFDQUMsY0FBYztZQUNaLEdBQUc7Z0JBQUVDLFdBQVc7WUFBRztZQUNuQixHQUFHO2dCQUFFQSxXQUFXO2dCQUFJQyxRQUFRO1lBQVE7UUFDdEM7SUFDRjtJQUVBLE9BQU9yRCxJQUFJc0QsYUFBYSxDQUFDQyxNQUFNLEdBQUc7QUFDcEM7QUFFQTs7Q0FFQyxHQUNELFNBQVN0QyxVQUFVakIsR0FBRyxFQUFFa0IsUUFBUTtJQUM5QixNQUFNc0QsWUFBWXhFLElBQUl5RSxRQUFRLENBQUNDLGdCQUFnQjtJQUUvQyxJQUFLLElBQUlDLElBQUksR0FBR0EsS0FBS0gsV0FBV0csSUFBSztRQUNuQzNFLElBQUk0RSxPQUFPLENBQUNEO1FBRVosY0FBYztRQUNkM0UsSUFBSTZFLFlBQVksQ0FBQyxLQUFLLEtBQUs7UUFDM0I3RSxJQUFJOEUsSUFBSSxDQUFDLElBQUksS0FBSyxLQUFLO1FBRXZCLGNBQWM7UUFDZDlFLElBQUl1QixXQUFXLENBQUM7UUFDaEJ2QixJQUFJd0IsT0FBTyxDQUFDLGFBQWE7UUFDekJ4QixJQUFJc0IsWUFBWSxDQUFDLEtBQUssS0FBSztRQUMzQnRCLElBQUl5QixJQUFJLENBQUMsc0RBQXNELElBQUk7UUFDbkV6QixJQUFJeUIsSUFBSSxDQUFDLENBQUMsS0FBSyxFQUFFa0QsRUFBRSxJQUFJLEVBQUVILFVBQVUsQ0FBQyxFQUFFLEtBQUs7UUFDM0N4RSxJQUFJeUIsSUFBSSxDQUFDLENBQUMsV0FBVyxFQUFFLElBQUlFLEtBQUtULFNBQVM2RCxXQUFXLEVBQUVDLGNBQWMsQ0FBQyxTQUFTLENBQUMsRUFBRSxJQUFJO0lBQ3ZGO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNELFNBQVNsRCxlQUFlK0IsTUFBTTtJQUM1QixPQUFPLElBQUlvQixLQUFLQyxZQUFZLENBQUMsU0FBUztRQUNwQ0MsT0FBTztRQUNQQyxVQUFVO0lBQ1osR0FBR0MsTUFBTSxDQUFDeEI7QUFDWjtBQUVBOztDQUVDLEdBQ0QsU0FBU25DLGdCQUFnQjNCLFNBQVM7SUFDaEMsTUFBTXVGLFNBQVM7UUFDYixhQUFhO1FBQ2IsY0FBYztRQUNkLGNBQWM7UUFDZCxZQUFZO0lBQ2Q7SUFFQSxPQUFPQSxNQUFNLENBQUN2RixVQUFVLElBQUlBO0FBQzlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2NlYW5zb3Vsc3BhcmtsZXMtYWRtaW4vLi9saWIvZXhwb3J0L3BkZi1nZW5lcmF0b3IuanM/Zjg0YiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBqc1BERiA9IHJlcXVpcmUoJ2pzcGRmJykuanNQREY7XG5yZXF1aXJlKCdqc3BkZi1hdXRvdGFibGUnKTtcblxuLyoqXG4gKiBHZW5lcmF0ZSBQREYgcmVwb3J0IGZyb20gcmVwb3J0IGRhdGFcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdlbmVyYXRlUERGUmVwb3J0KHJlcG9ydERhdGEsIGRhdGVSYW5nZSwgcmFuZ2VOYW1lKSB7XG4gIGNvbnN0IGRvYyA9IG5ldyBqc1BERigpO1xuICBcbiAgLy8gU2V0IHVwIGRvY3VtZW50IHByb3BlcnRpZXNcbiAgZG9jLnNldFByb3BlcnRpZXMoe1xuICAgIHRpdGxlOiAnT2NlYW4gU291bCBTcGFya2xlcyAtIEJ1c2luZXNzIFJlcG9ydCcsXG4gICAgc3ViamVjdDogYEJ1c2luZXNzIEFuYWx5dGljcyBSZXBvcnQgLSAke3JhbmdlTmFtZX1gLFxuICAgIGF1dGhvcjogJ09jZWFuIFNvdWwgU3BhcmtsZXMgQWRtaW4gU3lzdGVtJyxcbiAgICBjcmVhdG9yOiAnT2NlYW4gU291bCBTcGFya2xlcyBBZG1pbiBEYXNoYm9hcmQnXG4gIH0pO1xuXG4gIC8vIEFkZCBoZWFkZXJcbiAgYWRkSGVhZGVyKGRvYywgcmFuZ2VOYW1lLCBkYXRlUmFuZ2UpO1xuICBcbiAgbGV0IHlQb3NpdGlvbiA9IDYwO1xuXG4gIC8vIEFkZCBvdmVydmlldyBzZWN0aW9uXG4gIHlQb3NpdGlvbiA9IGFkZE92ZXJ2aWV3U2VjdGlvbihkb2MsIHJlcG9ydERhdGEub3ZlcnZpZXcsIHlQb3NpdGlvbik7XG4gIFxuICAvLyBBZGQgcmV2ZW51ZSBzZWN0aW9uXG4gIGlmICh5UG9zaXRpb24gPiAyMDApIHtcbiAgICBkb2MuYWRkUGFnZSgpO1xuICAgIHlQb3NpdGlvbiA9IDIwO1xuICB9XG4gIHlQb3NpdGlvbiA9IGFkZFJldmVudWVTZWN0aW9uKGRvYywgcmVwb3J0RGF0YS5yZXZlbnVlLCB5UG9zaXRpb24pO1xuICBcbiAgLy8gQWRkIGJvb2tpbmdzIHNlY3Rpb25cbiAgaWYgKHlQb3NpdGlvbiA+IDIwMCkge1xuICAgIGRvYy5hZGRQYWdlKCk7XG4gICAgeVBvc2l0aW9uID0gMjA7XG4gIH1cbiAgeVBvc2l0aW9uID0gYWRkQm9va2luZ3NTZWN0aW9uKGRvYywgcmVwb3J0RGF0YS5ib29raW5ncywgeVBvc2l0aW9uKTtcbiAgXG4gIC8vIEFkZCBjdXN0b21lcnMgc2VjdGlvblxuICBpZiAoeVBvc2l0aW9uID4gMjAwKSB7XG4gICAgZG9jLmFkZFBhZ2UoKTtcbiAgICB5UG9zaXRpb24gPSAyMDtcbiAgfVxuICB5UG9zaXRpb24gPSBhZGRDdXN0b21lcnNTZWN0aW9uKGRvYywgcmVwb3J0RGF0YS5jdXN0b21lcnMsIHlQb3NpdGlvbik7XG5cbiAgLy8gQWRkIGZvb3RlclxuICBhZGRGb290ZXIoZG9jLCByZXBvcnREYXRhLm1ldGFkYXRhKTtcblxuICByZXR1cm4gZG9jLm91dHB1dCgnYXJyYXlidWZmZXInKTtcbn1cblxuLyoqXG4gKiBBZGQgZG9jdW1lbnQgaGVhZGVyXG4gKi9cbmZ1bmN0aW9uIGFkZEhlYWRlcihkb2MsIHJhbmdlTmFtZSwgZGF0ZVJhbmdlKSB7XG4gIC8vIENvbXBhbnkgbG9nbyBhcmVhIChwbGFjZWhvbGRlcilcbiAgZG9jLnNldEZpbGxDb2xvcigxMDIsIDEyNiwgMjM0KTtcbiAgZG9jLnJlY3QoMjAsIDEwLCAxNzAsIDMwLCAnRicpO1xuICBcbiAgLy8gQ29tcGFueSBuYW1lXG4gIGRvYy5zZXRUZXh0Q29sb3IoMjU1LCAyNTUsIDI1NSk7XG4gIGRvYy5zZXRGb250U2l6ZSgyMCk7XG4gIGRvYy5zZXRGb250KCdoZWx2ZXRpY2EnLCAnYm9sZCcpO1xuICBkb2MudGV4dCgnT2NlYW4gU291bCBTcGFya2xlcycsIDI1LCAyNSk7XG4gIFxuICAvLyBSZXBvcnQgdGl0bGVcbiAgZG9jLnNldEZvbnRTaXplKDE0KTtcbiAgZG9jLnNldEZvbnQoJ2hlbHZldGljYScsICdub3JtYWwnKTtcbiAgZG9jLnRleHQoJ0J1c2luZXNzIEFuYWx5dGljcyBSZXBvcnQnLCAyNSwgMzUpO1xuICBcbiAgLy8gRGF0ZSByYW5nZVxuICBkb2Muc2V0VGV4dENvbG9yKDAsIDAsIDApO1xuICBkb2Muc2V0Rm9udFNpemUoMTIpO1xuICBkb2MudGV4dChgUmVwb3J0IFBlcmlvZDogJHtmb3JtYXREYXRlUmFuZ2UocmFuZ2VOYW1lKX1gLCAyMCwgNTApO1xuICBkb2MudGV4dChgR2VuZXJhdGVkOiAke25ldyBEYXRlKCkudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1BVScpfWAsIDEyMCwgNTApO1xufVxuXG4vKipcbiAqIEFkZCBvdmVydmlldyBtZXRyaWNzIHNlY3Rpb25cbiAqL1xuZnVuY3Rpb24gYWRkT3ZlcnZpZXdTZWN0aW9uKGRvYywgb3ZlcnZpZXcsIHlQb3NpdGlvbikge1xuICBkb2Muc2V0Rm9udFNpemUoMTYpO1xuICBkb2Muc2V0Rm9udCgnaGVsdmV0aWNhJywgJ2JvbGQnKTtcbiAgZG9jLnNldFRleHRDb2xvcigxMDIsIDEyNiwgMjM0KTtcbiAgZG9jLnRleHQoJ0V4ZWN1dGl2ZSBTdW1tYXJ5JywgMjAsIHlQb3NpdGlvbik7XG4gIFxuICB5UG9zaXRpb24gKz0gMTU7XG4gIFxuICAvLyBDcmVhdGUgb3ZlcnZpZXcgdGFibGVcbiAgY29uc3Qgb3ZlcnZpZXdEYXRhID0gW1xuICAgIFsnVG90YWwgUmV2ZW51ZScsIGZvcm1hdEN1cnJlbmN5KG92ZXJ2aWV3LnRvdGFsUmV2ZW51ZSksIGAke292ZXJ2aWV3LnJldmVudWVHcm93dGggPj0gMCA/ICcrJyA6ICcnfSR7b3ZlcnZpZXcucmV2ZW51ZUdyb3d0aC50b0ZpeGVkKDEpfSVgXSxcbiAgICBbJ1RvdGFsIEJvb2tpbmdzJywgb3ZlcnZpZXcudG90YWxCb29raW5ncy50b1N0cmluZygpLCBgJHtvdmVydmlldy5ib29raW5nR3Jvd3RoID49IDAgPyAnKycgOiAnJ30ke292ZXJ2aWV3LmJvb2tpbmdHcm93dGgudG9GaXhlZCgxKX0lYF0sXG4gICAgWydUb3RhbCBDdXN0b21lcnMnLCBvdmVydmlldy50b3RhbEN1c3RvbWVycy50b1N0cmluZygpLCAnLSddLFxuICAgIFsnQXZlcmFnZSBCb29raW5nIFZhbHVlJywgZm9ybWF0Q3VycmVuY3kob3ZlcnZpZXcuYXZlcmFnZUJvb2tpbmdWYWx1ZSksICctJ11cbiAgXTtcblxuICBkb2MuYXV0b1RhYmxlKHtcbiAgICBzdGFydFk6IHlQb3NpdGlvbixcbiAgICBoZWFkOiBbWydNZXRyaWMnLCAnVmFsdWUnLCAnR3Jvd3RoJ11dLFxuICAgIGJvZHk6IG92ZXJ2aWV3RGF0YSxcbiAgICB0aGVtZTogJ2dyaWQnLFxuICAgIGhlYWRTdHlsZXM6IHsgXG4gICAgICBmaWxsQ29sb3I6IFsxMDIsIDEyNiwgMjM0XSxcbiAgICAgIHRleHRDb2xvcjogWzI1NSwgMjU1LCAyNTVdLFxuICAgICAgZm9udFN0eWxlOiAnYm9sZCdcbiAgICB9LFxuICAgIHN0eWxlczogeyBcbiAgICAgIGZvbnRTaXplOiAxMCxcbiAgICAgIGNlbGxQYWRkaW5nOiA1XG4gICAgfSxcbiAgICBjb2x1bW5TdHlsZXM6IHtcbiAgICAgIDA6IHsgY2VsbFdpZHRoOiA2MCB9LFxuICAgICAgMTogeyBjZWxsV2lkdGg6IDUwLCBoYWxpZ246ICdyaWdodCcgfSxcbiAgICAgIDI6IHsgY2VsbFdpZHRoOiAzMCwgaGFsaWduOiAnY2VudGVyJyB9XG4gICAgfVxuICB9KTtcblxuICByZXR1cm4gZG9jLmxhc3RBdXRvVGFibGUuZmluYWxZICsgMjA7XG59XG5cbi8qKlxuICogQWRkIHJldmVudWUgYW5hbHlzaXMgc2VjdGlvblxuICovXG5mdW5jdGlvbiBhZGRSZXZlbnVlU2VjdGlvbihkb2MsIHJldmVudWUsIHlQb3NpdGlvbikge1xuICBkb2Muc2V0Rm9udFNpemUoMTYpO1xuICBkb2Muc2V0Rm9udCgnaGVsdmV0aWNhJywgJ2JvbGQnKTtcbiAgZG9jLnNldFRleHRDb2xvcigxMDIsIDEyNiwgMjM0KTtcbiAgZG9jLnRleHQoJ1JldmVudWUgQW5hbHlzaXMnLCAyMCwgeVBvc2l0aW9uKTtcbiAgXG4gIHlQb3NpdGlvbiArPSAxNTtcblxuICAvLyBSZXZlbnVlIGJ5IHNlcnZpY2UgdGFibGVcbiAgaWYgKHJldmVudWUuYnlTZXJ2aWNlICYmIHJldmVudWUuYnlTZXJ2aWNlLmxlbmd0aCA+IDApIHtcbiAgICBkb2Muc2V0Rm9udFNpemUoMTIpO1xuICAgIGRvYy5zZXRGb250KCdoZWx2ZXRpY2EnLCAnYm9sZCcpO1xuICAgIGRvYy5zZXRUZXh0Q29sb3IoMCwgMCwgMCk7XG4gICAgZG9jLnRleHQoJ1JldmVudWUgYnkgU2VydmljZScsIDIwLCB5UG9zaXRpb24pO1xuICAgIHlQb3NpdGlvbiArPSAxMDtcblxuICAgIGNvbnN0IHNlcnZpY2VEYXRhID0gcmV2ZW51ZS5ieVNlcnZpY2UubWFwKHNlcnZpY2UgPT4gW1xuICAgICAgc2VydmljZS5zZXJ2aWNlLFxuICAgICAgZm9ybWF0Q3VycmVuY3koc2VydmljZS5hbW91bnQpLFxuICAgICAgYCR7c2VydmljZS5wZXJjZW50YWdlLnRvRml4ZWQoMSl9JWBcbiAgICBdKTtcblxuICAgIGRvYy5hdXRvVGFibGUoe1xuICAgICAgc3RhcnRZOiB5UG9zaXRpb24sXG4gICAgICBoZWFkOiBbWydTZXJ2aWNlJywgJ1JldmVudWUnLCAnUGVyY2VudGFnZSddXSxcbiAgICAgIGJvZHk6IHNlcnZpY2VEYXRhLFxuICAgICAgdGhlbWU6ICdzdHJpcGVkJyxcbiAgICAgIGhlYWRTdHlsZXM6IHsgXG4gICAgICAgIGZpbGxDb2xvcjogWzExOCwgNzUsIDE2Ml0sXG4gICAgICAgIHRleHRDb2xvcjogWzI1NSwgMjU1LCAyNTVdLFxuICAgICAgICBmb250U3R5bGU6ICdib2xkJ1xuICAgICAgfSxcbiAgICAgIHN0eWxlczogeyBcbiAgICAgICAgZm9udFNpemU6IDksXG4gICAgICAgIGNlbGxQYWRkaW5nOiA0XG4gICAgICB9LFxuICAgICAgY29sdW1uU3R5bGVzOiB7XG4gICAgICAgIDA6IHsgY2VsbFdpZHRoOiA4MCB9LFxuICAgICAgICAxOiB7IGNlbGxXaWR0aDogNDAsIGhhbGlnbjogJ3JpZ2h0JyB9LFxuICAgICAgICAyOiB7IGNlbGxXaWR0aDogMzAsIGhhbGlnbjogJ2NlbnRlcicgfVxuICAgICAgfVxuICAgIH0pO1xuXG4gICAgeVBvc2l0aW9uID0gZG9jLmxhc3RBdXRvVGFibGUuZmluYWxZICsgMTU7XG4gIH1cblxuICByZXR1cm4geVBvc2l0aW9uO1xufVxuXG4vKipcbiAqIEFkZCBib29raW5ncyBhbmFseXNpcyBzZWN0aW9uXG4gKi9cbmZ1bmN0aW9uIGFkZEJvb2tpbmdzU2VjdGlvbihkb2MsIGJvb2tpbmdzLCB5UG9zaXRpb24pIHtcbiAgZG9jLnNldEZvbnRTaXplKDE2KTtcbiAgZG9jLnNldEZvbnQoJ2hlbHZldGljYScsICdib2xkJyk7XG4gIGRvYy5zZXRUZXh0Q29sb3IoMTAyLCAxMjYsIDIzNCk7XG4gIGRvYy50ZXh0KCdCb29raW5nIEFuYWx5c2lzJywgMjAsIHlQb3NpdGlvbik7XG4gIFxuICB5UG9zaXRpb24gKz0gMTU7XG5cbiAgLy8gQm9va2luZyBzdGF0dXMgYnJlYWtkb3duXG4gIGlmIChib29raW5ncy5zdGF0dXNCcmVha2Rvd24gJiYgYm9va2luZ3Muc3RhdHVzQnJlYWtkb3duLmxlbmd0aCA+IDApIHtcbiAgICBkb2Muc2V0Rm9udFNpemUoMTIpO1xuICAgIGRvYy5zZXRGb250KCdoZWx2ZXRpY2EnLCAnYm9sZCcpO1xuICAgIGRvYy5zZXRUZXh0Q29sb3IoMCwgMCwgMCk7XG4gICAgZG9jLnRleHQoJ0Jvb2tpbmcgU3RhdHVzIEJyZWFrZG93bicsIDIwLCB5UG9zaXRpb24pO1xuICAgIHlQb3NpdGlvbiArPSAxMDtcblxuICAgIGNvbnN0IHN0YXR1c0RhdGEgPSBib29raW5ncy5zdGF0dXNCcmVha2Rvd24ubWFwKHN0YXR1cyA9PiBbXG4gICAgICBzdGF0dXMuc3RhdHVzLFxuICAgICAgc3RhdHVzLmNvdW50LnRvU3RyaW5nKCksXG4gICAgICBgJHtzdGF0dXMucGVyY2VudGFnZS50b0ZpeGVkKDEpfSVgXG4gICAgXSk7XG5cbiAgICBkb2MuYXV0b1RhYmxlKHtcbiAgICAgIHN0YXJ0WTogeVBvc2l0aW9uLFxuICAgICAgaGVhZDogW1snU3RhdHVzJywgJ0NvdW50JywgJ1BlcmNlbnRhZ2UnXV0sXG4gICAgICBib2R5OiBzdGF0dXNEYXRhLFxuICAgICAgdGhlbWU6ICdzdHJpcGVkJyxcbiAgICAgIGhlYWRTdHlsZXM6IHsgXG4gICAgICAgIGZpbGxDb2xvcjogWzM0LCAxOTcsIDk0XSxcbiAgICAgICAgdGV4dENvbG9yOiBbMjU1LCAyNTUsIDI1NV0sXG4gICAgICAgIGZvbnRTdHlsZTogJ2JvbGQnXG4gICAgICB9LFxuICAgICAgc3R5bGVzOiB7IFxuICAgICAgICBmb250U2l6ZTogOSxcbiAgICAgICAgY2VsbFBhZGRpbmc6IDRcbiAgICAgIH0sXG4gICAgICBjb2x1bW5TdHlsZXM6IHtcbiAgICAgICAgMDogeyBjZWxsV2lkdGg6IDYwIH0sXG4gICAgICAgIDE6IHsgY2VsbFdpZHRoOiAzMCwgaGFsaWduOiAnY2VudGVyJyB9LFxuICAgICAgICAyOiB7IGNlbGxXaWR0aDogMzAsIGhhbGlnbjogJ2NlbnRlcicgfVxuICAgICAgfVxuICAgIH0pO1xuXG4gICAgeVBvc2l0aW9uID0gZG9jLmxhc3RBdXRvVGFibGUuZmluYWxZICsgMTA7XG4gIH1cblxuICAvLyBDYW5jZWxsYXRpb24gcmF0ZVxuICBkb2Muc2V0Rm9udFNpemUoMTApO1xuICBkb2Muc2V0Rm9udCgnaGVsdmV0aWNhJywgJ25vcm1hbCcpO1xuICBkb2MudGV4dChgQ2FuY2VsbGF0aW9uIFJhdGU6ICR7Ym9va2luZ3MuY2FuY2VsbGF0aW9uUmF0ZS50b0ZpeGVkKDEpfSVgLCAyMCwgeVBvc2l0aW9uKTtcblxuICByZXR1cm4geVBvc2l0aW9uICsgMTU7XG59XG5cbi8qKlxuICogQWRkIGN1c3RvbWVycyBhbmFseXNpcyBzZWN0aW9uXG4gKi9cbmZ1bmN0aW9uIGFkZEN1c3RvbWVyc1NlY3Rpb24oZG9jLCBjdXN0b21lcnMsIHlQb3NpdGlvbikge1xuICBkb2Muc2V0Rm9udFNpemUoMTYpO1xuICBkb2Muc2V0Rm9udCgnaGVsdmV0aWNhJywgJ2JvbGQnKTtcbiAgZG9jLnNldFRleHRDb2xvcigxMDIsIDEyNiwgMjM0KTtcbiAgZG9jLnRleHQoJ0N1c3RvbWVyIEFuYWx5c2lzJywgMjAsIHlQb3NpdGlvbik7XG4gIFxuICB5UG9zaXRpb24gKz0gMTU7XG5cbiAgLy8gQ3VzdG9tZXIgbWV0cmljcyB0YWJsZVxuICBjb25zdCBjdXN0b21lckRhdGEgPSBbXG4gICAgWydOZXcgQ3VzdG9tZXJzJywgY3VzdG9tZXJzLm5ld0N1c3RvbWVycy50b1N0cmluZygpXSxcbiAgICBbJ1JldHVybmluZyBDdXN0b21lcnMnLCBjdXN0b21lcnMucmV0dXJuaW5nQ3VzdG9tZXJzLnRvU3RyaW5nKCldLFxuICAgIFsnQ3VzdG9tZXIgTGlmZXRpbWUgVmFsdWUnLCBmb3JtYXRDdXJyZW5jeShjdXN0b21lcnMuY3VzdG9tZXJMaWZldGltZVZhbHVlKV1cbiAgXTtcblxuICBkb2MuYXV0b1RhYmxlKHtcbiAgICBzdGFydFk6IHlQb3NpdGlvbixcbiAgICBoZWFkOiBbWydNZXRyaWMnLCAnVmFsdWUnXV0sXG4gICAgYm9keTogY3VzdG9tZXJEYXRhLFxuICAgIHRoZW1lOiAnZ3JpZCcsXG4gICAgaGVhZFN0eWxlczogeyBcbiAgICAgIGZpbGxDb2xvcjogWzU5LCAxMzAsIDI0Nl0sXG4gICAgICB0ZXh0Q29sb3I6IFsyNTUsIDI1NSwgMjU1XSxcbiAgICAgIGZvbnRTdHlsZTogJ2JvbGQnXG4gICAgfSxcbiAgICBzdHlsZXM6IHsgXG4gICAgICBmb250U2l6ZTogMTAsXG4gICAgICBjZWxsUGFkZGluZzogNVxuICAgIH0sXG4gICAgY29sdW1uU3R5bGVzOiB7XG4gICAgICAwOiB7IGNlbGxXaWR0aDogODAgfSxcbiAgICAgIDE6IHsgY2VsbFdpZHRoOiA1MCwgaGFsaWduOiAncmlnaHQnIH1cbiAgICB9XG4gIH0pO1xuXG4gIHJldHVybiBkb2MubGFzdEF1dG9UYWJsZS5maW5hbFkgKyAyMDtcbn1cblxuLyoqXG4gKiBBZGQgZG9jdW1lbnQgZm9vdGVyXG4gKi9cbmZ1bmN0aW9uIGFkZEZvb3Rlcihkb2MsIG1ldGFkYXRhKSB7XG4gIGNvbnN0IHBhZ2VDb3VudCA9IGRvYy5pbnRlcm5hbC5nZXROdW1iZXJPZlBhZ2VzKCk7XG4gIFxuICBmb3IgKGxldCBpID0gMTsgaSA8PSBwYWdlQ291bnQ7IGkrKykge1xuICAgIGRvYy5zZXRQYWdlKGkpO1xuICAgIFxuICAgIC8vIEZvb3RlciBsaW5lXG4gICAgZG9jLnNldERyYXdDb2xvcigyMDAsIDIwMCwgMjAwKTtcbiAgICBkb2MubGluZSgyMCwgMjgwLCAxOTAsIDI4MCk7XG4gICAgXG4gICAgLy8gRm9vdGVyIHRleHRcbiAgICBkb2Muc2V0Rm9udFNpemUoOCk7XG4gICAgZG9jLnNldEZvbnQoJ2hlbHZldGljYScsICdub3JtYWwnKTtcbiAgICBkb2Muc2V0VGV4dENvbG9yKDEwMCwgMTAwLCAxMDApO1xuICAgIGRvYy50ZXh0KCdPY2VhbiBTb3VsIFNwYXJrbGVzIC0gQ29uZmlkZW50aWFsIEJ1c2luZXNzIFJlcG9ydCcsIDIwLCAyODUpO1xuICAgIGRvYy50ZXh0KGBQYWdlICR7aX0gb2YgJHtwYWdlQ291bnR9YCwgMTYwLCAyODUpO1xuICAgIGRvYy50ZXh0KGBHZW5lcmF0ZWQ6ICR7bmV3IERhdGUobWV0YWRhdGEuZ2VuZXJhdGVkQXQpLnRvTG9jYWxlU3RyaW5nKCdlbi1BVScpfWAsIDIwLCAyOTApO1xuICB9XG59XG5cbi8qKlxuICogRm9ybWF0IGN1cnJlbmN5IGZvciBkaXNwbGF5XG4gKi9cbmZ1bmN0aW9uIGZvcm1hdEN1cnJlbmN5KGFtb3VudCkge1xuICByZXR1cm4gbmV3IEludGwuTnVtYmVyRm9ybWF0KCdlbi1BVScsIHtcbiAgICBzdHlsZTogJ2N1cnJlbmN5JyxcbiAgICBjdXJyZW5jeTogJ0FVRCdcbiAgfSkuZm9ybWF0KGFtb3VudCk7XG59XG5cbi8qKlxuICogRm9ybWF0IGRhdGUgcmFuZ2UgZm9yIGRpc3BsYXlcbiAqL1xuZnVuY3Rpb24gZm9ybWF0RGF0ZVJhbmdlKHJhbmdlTmFtZSkge1xuICBjb25zdCByYW5nZXMgPSB7XG4gICAgJ2xhc3Q3ZGF5cyc6ICdMYXN0IDcgRGF5cycsXG4gICAgJ2xhc3QzMGRheXMnOiAnTGFzdCAzMCBEYXlzJyxcbiAgICAnbGFzdDkwZGF5cyc6ICdMYXN0IDkwIERheXMnLFxuICAgICd0aGlzeWVhcic6ICdUaGlzIFllYXInXG4gIH07XG4gIFxuICByZXR1cm4gcmFuZ2VzW3JhbmdlTmFtZV0gfHwgcmFuZ2VOYW1lO1xufVxuIl0sIm5hbWVzIjpbImpzUERGIiwicmVxdWlyZSIsImdlbmVyYXRlUERGUmVwb3J0IiwicmVwb3J0RGF0YSIsImRhdGVSYW5nZSIsInJhbmdlTmFtZSIsImRvYyIsInNldFByb3BlcnRpZXMiLCJ0aXRsZSIsInN1YmplY3QiLCJhdXRob3IiLCJjcmVhdG9yIiwiYWRkSGVhZGVyIiwieVBvc2l0aW9uIiwiYWRkT3ZlcnZpZXdTZWN0aW9uIiwib3ZlcnZpZXciLCJhZGRQYWdlIiwiYWRkUmV2ZW51ZVNlY3Rpb24iLCJyZXZlbnVlIiwiYWRkQm9va2luZ3NTZWN0aW9uIiwiYm9va2luZ3MiLCJhZGRDdXN0b21lcnNTZWN0aW9uIiwiY3VzdG9tZXJzIiwiYWRkRm9vdGVyIiwibWV0YWRhdGEiLCJvdXRwdXQiLCJzZXRGaWxsQ29sb3IiLCJyZWN0Iiwic2V0VGV4dENvbG9yIiwic2V0Rm9udFNpemUiLCJzZXRGb250IiwidGV4dCIsImZvcm1hdERhdGVSYW5nZSIsIkRhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJvdmVydmlld0RhdGEiLCJmb3JtYXRDdXJyZW5jeSIsInRvdGFsUmV2ZW51ZSIsInJldmVudWVHcm93dGgiLCJ0b0ZpeGVkIiwidG90YWxCb29raW5ncyIsInRvU3RyaW5nIiwiYm9va2luZ0dyb3d0aCIsInRvdGFsQ3VzdG9tZXJzIiwiYXZlcmFnZUJvb2tpbmdWYWx1ZSIsImF1dG9UYWJsZSIsInN0YXJ0WSIsImhlYWQiLCJib2R5IiwidGhlbWUiLCJoZWFkU3R5bGVzIiwiZmlsbENvbG9yIiwidGV4dENvbG9yIiwiZm9udFN0eWxlIiwic3R5bGVzIiwiZm9udFNpemUiLCJjZWxsUGFkZGluZyIsImNvbHVtblN0eWxlcyIsImNlbGxXaWR0aCIsImhhbGlnbiIsImxhc3RBdXRvVGFibGUiLCJmaW5hbFkiLCJieVNlcnZpY2UiLCJsZW5ndGgiLCJzZXJ2aWNlRGF0YSIsIm1hcCIsInNlcnZpY2UiLCJhbW91bnQiLCJwZXJjZW50YWdlIiwic3RhdHVzQnJlYWtkb3duIiwic3RhdHVzRGF0YSIsInN0YXR1cyIsImNvdW50IiwiY2FuY2VsbGF0aW9uUmF0ZSIsImN1c3RvbWVyRGF0YSIsIm5ld0N1c3RvbWVycyIsInJldHVybmluZ0N1c3RvbWVycyIsImN1c3RvbWVyTGlmZXRpbWVWYWx1ZSIsInBhZ2VDb3VudCIsImludGVybmFsIiwiZ2V0TnVtYmVyT2ZQYWdlcyIsImkiLCJzZXRQYWdlIiwic2V0RHJhd0NvbG9yIiwibGluZSIsImdlbmVyYXRlZEF0IiwidG9Mb2NhbGVTdHJpbmciLCJJbnRsIiwiTnVtYmVyRm9ybWF0Iiwic3R5bGUiLCJjdXJyZW5jeSIsImZvcm1hdCIsInJhbmdlcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./lib/export/pdf-generator.js\n");

/***/ }),

/***/ "(api)/./lib/security/audit-logging.ts":
/*!***************************************!*\
  !*** ./lib/security/audit-logging.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuditActions: () => (/* binding */ AuditActions),\n/* harmony export */   auditLog: () => (/* binding */ auditLog),\n/* harmony export */   exportAuditLogs: () => (/* binding */ exportAuditLogs),\n/* harmony export */   getAuditLogs: () => (/* binding */ getAuditLogs),\n/* harmony export */   logCriticalEvent: () => (/* binding */ logCriticalEvent),\n/* harmony export */   logDataChange: () => (/* binding */ logDataChange),\n/* harmony export */   logSecurityEvent: () => (/* binding */ logSecurityEvent),\n/* harmony export */   logUserAction: () => (/* binding */ logUserAction)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\n// Handle missing environment variables gracefully\nconst supabaseUrl = \"https://ndlgbcsbidyhxbpqzgqp.supabase.co\" || 0;\nconst supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseKey);\n/**\r\n * Log audit event to database and console\r\n */ async function auditLog(entry) {\n    try {\n        const logEntry = {\n            action: entry.action,\n            user_id: entry.userId,\n            user_role: entry.userRole,\n            email: entry.email,\n            ip_address: entry.ip,\n            path: entry.path,\n            resource: entry.resource,\n            resource_id: entry.resourceId,\n            old_values: entry.oldValues,\n            new_values: entry.newValues,\n            reason: entry.reason,\n            error: entry.error,\n            metadata: entry.metadata,\n            severity: entry.severity || \"medium\",\n            created_at: new Date().toISOString()\n        };\n        // Log to database\n        const { error } = await supabase.from(\"audit_logs\").insert(logEntry);\n        if (error) {\n            console.error(\"Failed to write audit log to database:\", error);\n        }\n        // Log to console for immediate visibility\n        const logLevel = getLogLevel(entry.severity || \"medium\");\n        const logMessage = formatLogMessage(entry);\n        console[logLevel](logMessage);\n        // For critical events, also send alerts\n        if (entry.severity === \"critical\") {\n            await sendCriticalAlert(entry);\n        }\n    } catch (error) {\n        console.error(\"Audit logging failed:\", error);\n        // Fallback to console logging\n        console.error(\"AUDIT_LOG_FAILURE:\", JSON.stringify(entry, null, 2));\n    }\n}\n/**\r\n * Get appropriate console log level based on severity\r\n */ function getLogLevel(severity) {\n    switch(severity){\n        case \"low\":\n            return \"log\";\n        case \"medium\":\n            return \"log\";\n        case \"high\":\n            return \"warn\";\n        case \"critical\":\n            return \"error\";\n        default:\n            return \"log\";\n    }\n}\n/**\r\n * Format audit log message for console output\r\n */ function formatLogMessage(entry) {\n    const timestamp = new Date().toISOString();\n    const user = entry.userId ? `[User: ${entry.userId}]` : \"\";\n    const ip = entry.ip ? `[IP: ${entry.ip}]` : \"\";\n    const path = entry.path ? `[Path: ${entry.path}]` : \"\";\n    return `[AUDIT] ${timestamp} ${entry.action} ${user} ${ip} ${path} ${entry.reason || \"\"}`.trim();\n}\n/**\r\n * Send critical alert for high-severity events\r\n */ async function sendCriticalAlert(entry) {\n    try {\n        // In production, this would send alerts via:\n        // - Email to admin team\n        // - Slack/Discord webhook\n        // - SMS for critical security events\n        // - Push notifications\n        console.error(\"\\uD83D\\uDEA8 CRITICAL SECURITY EVENT:\", {\n            action: entry.action,\n            userId: entry.userId,\n            ip: entry.ip,\n            reason: entry.reason,\n            timestamp: new Date().toISOString()\n        });\n        // Example: Send email alert (implement based on your email service)\n        if (process.env.ENABLE_CRITICAL_ALERTS === \"true\") {\n            await sendEmailAlert(entry);\n        }\n    } catch (error) {\n        console.error(\"Failed to send critical alert:\", error);\n    }\n}\n/**\r\n * Send email alert for critical events\r\n */ async function sendEmailAlert(entry) {\n    // Implementation would depend on your email service\n    // This is a placeholder for the actual implementation\n    console.log(\"Email alert would be sent for:\", entry.action);\n}\n/**\r\n * Audit log helper functions for common actions\r\n */ const AuditActions = {\n    // Authentication events\n    LOGIN_SUCCESS: \"LOGIN_SUCCESS\",\n    LOGIN_FAILED: \"LOGIN_FAILED\",\n    LOGIN_BLOCKED: \"LOGIN_BLOCKED\",\n    LOGOUT: \"LOGOUT\",\n    MFA_ENABLED: \"MFA_ENABLED\",\n    MFA_DISABLED: \"MFA_DISABLED\",\n    MFA_FAILED: \"MFA_FAILED\",\n    PASSWORD_CHANGED: \"PASSWORD_CHANGED\",\n    PASSWORD_RESET: \"PASSWORD_RESET\",\n    // Access control events\n    ACCESS_GRANTED: \"ACCESS_GRANTED\",\n    ACCESS_DENIED: \"ACCESS_DENIED\",\n    UNAUTHORIZED_ACCESS: \"UNAUTHORIZED_ACCESS\",\n    INSUFFICIENT_PERMISSIONS: \"INSUFFICIENT_PERMISSIONS\",\n    SESSION_TIMEOUT: \"SESSION_TIMEOUT\",\n    // Data modification events\n    RECORD_CREATED: \"RECORD_CREATED\",\n    RECORD_UPDATED: \"RECORD_UPDATED\",\n    RECORD_DELETED: \"RECORD_DELETED\",\n    BULK_UPDATE: \"BULK_UPDATE\",\n    BULK_DELETE: \"BULK_DELETE\",\n    // Admin actions\n    USER_CREATED: \"USER_CREATED\",\n    USER_UPDATED: \"USER_UPDATED\",\n    USER_DEACTIVATED: \"USER_DEACTIVATED\",\n    ROLE_CHANGED: \"ROLE_CHANGED\",\n    PERMISSIONS_CHANGED: \"PERMISSIONS_CHANGED\",\n    // System events\n    SYSTEM_ERROR: \"SYSTEM_ERROR\",\n    CONFIGURATION_CHANGED: \"CONFIGURATION_CHANGED\",\n    BACKUP_CREATED: \"BACKUP_CREATED\",\n    BACKUP_RESTORED: \"BACKUP_RESTORED\",\n    // Security events\n    IP_BLOCKED: \"IP_BLOCKED\",\n    RATE_LIMITED: \"RATE_LIMITED\",\n    SUSPICIOUS_ACTIVITY: \"SUSPICIOUS_ACTIVITY\",\n    SECURITY_SCAN: \"SECURITY_SCAN\",\n    VULNERABILITY_DETECTED: \"VULNERABILITY_DETECTED\",\n    // Business events\n    BOOKING_CREATED: \"BOOKING_CREATED\",\n    BOOKING_MODIFIED: \"BOOKING_MODIFIED\",\n    BOOKING_CANCELLED: \"BOOKING_CANCELLED\",\n    PAYMENT_PROCESSED: \"PAYMENT_PROCESSED\",\n    REFUND_ISSUED: \"REFUND_ISSUED\"\n};\n/**\r\n * Helper function to log user actions\r\n */ async function logUserAction(action, userId, userRole, details = {}) {\n    await auditLog({\n        action,\n        userId,\n        userRole,\n        severity: \"medium\",\n        ...details\n    });\n}\n/**\r\n * Helper function to log security events\r\n */ async function logSecurityEvent(action, details = {}) {\n    await auditLog({\n        action,\n        severity: \"high\",\n        ...details\n    });\n}\n/**\r\n * Helper function to log critical security events\r\n */ async function logCriticalEvent(action, details = {}) {\n    await auditLog({\n        action,\n        severity: \"critical\",\n        ...details\n    });\n}\n/**\r\n * Helper function to log data changes\r\n */ async function logDataChange(action, userId, resource, resourceId, oldValues, newValues) {\n    await auditLog({\n        action,\n        userId,\n        resource,\n        resourceId,\n        oldValues,\n        newValues,\n        severity: \"medium\"\n    });\n}\n/**\r\n * Get audit logs with filtering and pagination\r\n */ async function getAuditLogs(filters) {\n    try {\n        let query = supabase.from(\"audit_logs\").select(\"*\").order(\"created_at\", {\n            ascending: false\n        });\n        if (filters.userId) {\n            query = query.eq(\"user_id\", filters.userId);\n        }\n        if (filters.action) {\n            query = query.eq(\"action\", filters.action);\n        }\n        if (filters.severity) {\n            query = query.eq(\"severity\", filters.severity);\n        }\n        if (filters.startDate) {\n            query = query.gte(\"created_at\", filters.startDate);\n        }\n        if (filters.endDate) {\n            query = query.lte(\"created_at\", filters.endDate);\n        }\n        if (filters.limit) {\n            query = query.limit(filters.limit);\n        }\n        if (filters.offset) {\n            query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw error;\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error(\"Error fetching audit logs:\", error);\n        return {\n            data: null,\n            error\n        };\n    }\n}\n/**\r\n * Export audit logs for compliance\r\n */ async function exportAuditLogs(startDate, endDate, format = \"json\") {\n    try {\n        const { data, error } = await supabase.from(\"audit_logs\").select(\"*\").gte(\"created_at\", startDate).lte(\"created_at\", endDate).order(\"created_at\", {\n            ascending: true\n        });\n        if (error) {\n            throw error;\n        }\n        if (format === \"csv\") {\n            return convertToCSV(data);\n        }\n        return data;\n    } catch (error) {\n        console.error(\"Error exporting audit logs:\", error);\n        throw error;\n    }\n}\n/**\r\n * Convert audit logs to CSV format\r\n */ function convertToCSV(data) {\n    if (!data || data.length === 0) {\n        return \"\";\n    }\n    const headers = Object.keys(data[0]);\n    const csvContent = [\n        headers.join(\",\"),\n        ...data.map((row)=>headers.map((header)=>{\n                const value = row[header];\n                if (typeof value === \"object\" && value !== null) {\n                    return `\"${JSON.stringify(value).replace(/\"/g, '\"\"')}\"`;\n                }\n                return `\"${String(value || \"\").replace(/\"/g, '\"\"')}\"`;\n            }).join(\",\"))\n    ].join(\"\\n\");\n    return csvContent;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/security/audit-logging.ts\n");

/***/ }),

/***/ "(api)/./lib/supabase-admin.js":
/*!*******************************!*\
  !*** ./lib/supabase-admin.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://ndlgbcsbidyhxbpqzgqp.supabase.co\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI\";\nif (!supabaseUrl || !supabaseServiceRoleKey) {\n    console.warn(\"Missing Supabase environment variables for admin client\");\n}\n// Admin client for server-side operations (bypasses RLS)\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceRoleKey || supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWIvc3VwYWJhc2UtYWRtaW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW9EO0FBRXBELE1BQU1DLGNBQWNDLDBDQUFvQztBQUN4RCxNQUFNRyx5QkFBeUJILFFBQVFDLEdBQUcsQ0FBQ0cseUJBQXlCO0FBQ3BFLE1BQU1DLGtCQUFrQkwsa05BQXlDO0FBRWpFLElBQUksQ0FBQ0QsZUFBZSxDQUFDSSx3QkFBd0I7SUFDM0NJLFFBQVFDLElBQUksQ0FBQztBQUNmO0FBRUEseURBQXlEO0FBQ2xELE1BQU1DLGdCQUFnQlgsbUVBQVlBLENBQ3ZDQyxhQUNBSSwwQkFBMEJFLGlCQUMxQjtJQUNFSyxNQUFNO1FBQ0pDLGtCQUFrQjtRQUNsQkMsZ0JBQWdCO0lBQ2xCO0FBQ0YsR0FDRCIsInNvdXJjZXMiOlsid2VicGFjazovL29jZWFuc291bHNwYXJrbGVzLWFkbWluLy4vbGliL3N1cGFiYXNlLWFkbWluLmpzPzBjOWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJ1xuXG5jb25zdCBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTFxuY29uc3Qgc3VwYWJhc2VTZXJ2aWNlUm9sZUtleSA9IHByb2Nlc3MuZW52LlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVlcbmNvbnN0IHN1cGFiYXNlQW5vbktleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZXG5cbmlmICghc3VwYWJhc2VVcmwgfHwgIXN1cGFiYXNlU2VydmljZVJvbGVLZXkpIHtcbiAgY29uc29sZS53YXJuKCdNaXNzaW5nIFN1cGFiYXNlIGVudmlyb25tZW50IHZhcmlhYmxlcyBmb3IgYWRtaW4gY2xpZW50Jylcbn1cblxuLy8gQWRtaW4gY2xpZW50IGZvciBzZXJ2ZXItc2lkZSBvcGVyYXRpb25zIChieXBhc3NlcyBSTFMpXG5leHBvcnQgY29uc3Qgc3VwYWJhc2VBZG1pbiA9IGNyZWF0ZUNsaWVudChcbiAgc3VwYWJhc2VVcmwsXG4gIHN1cGFiYXNlU2VydmljZVJvbGVLZXkgfHwgc3VwYWJhc2VBbm9uS2V5LFxuICB7XG4gICAgYXV0aDoge1xuICAgICAgYXV0b1JlZnJlc2hUb2tlbjogZmFsc2UsXG4gICAgICBwZXJzaXN0U2Vzc2lvbjogZmFsc2VcbiAgICB9XG4gIH1cbilcbiJdLCJuYW1lcyI6WyJjcmVhdGVDbGllbnQiLCJzdXBhYmFzZVVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJzdXBhYmFzZVNlcnZpY2VSb2xlS2V5IiwiU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSIsInN1cGFiYXNlQW5vbktleSIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwiY29uc29sZSIsIndhcm4iLCJzdXBhYmFzZUFkbWluIiwiYXV0aCIsImF1dG9SZWZyZXNoVG9rZW4iLCJwZXJzaXN0U2Vzc2lvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./lib/supabase-admin.js\n");

/***/ }),

/***/ "(api)/./pages/api/admin/reports/export.js":
/*!*******************************************!*\
  !*** ./pages/api/admin/reports/export.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_auth_admin_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../lib/auth/admin-auth */ \"(api)/./lib/auth/admin-auth.ts\");\n/* harmony import */ var _lib_export_pdf_generator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../lib/export/pdf-generator */ \"(api)/./lib/export/pdf-generator.js\");\n/* harmony import */ var _lib_export_excel_generator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../lib/export/excel-generator */ \"(api)/./lib/export/excel-generator.js\");\n/* harmony import */ var _lib_export_csv_generator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../lib/export/csv-generator */ \"(api)/./lib/export/csv-generator.js\");\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../lib/supabase-admin */ \"(api)/./lib/supabase-admin.js\");\n\n\n\n\n\n/**\n * Reports Export API Endpoint\n * \n * Handles exporting reports in various formats (PDF, Excel, CSV)\n * Supports different date ranges and report types\n */ async function handler(req, res) {\n    // Generate unique request ID for tracking\n    const requestId = `export-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n    try {\n        console.log(`[${requestId}] Export request started:`, {\n            method: req.method,\n            query: req.query,\n            userAgent: req.headers[\"user-agent\"]\n        });\n        // Only allow GET requests\n        if (req.method !== \"GET\") {\n            return res.status(405).json({\n                error: \"Method not allowed\",\n                requestId\n            });\n        }\n        // Verify admin authentication\n        const authResult = await (0,_lib_auth_admin_auth__WEBPACK_IMPORTED_MODULE_0__.verifyAdminToken)(req);\n        if (!authResult.valid) {\n            console.log(`[${requestId}] Authentication failed:`, authResult.error);\n            return res.status(401).json({\n                error: \"Unauthorized\",\n                requestId\n            });\n        }\n        // Check admin permissions\n        if (![\n            \"DEV\",\n            \"Admin\"\n        ].includes(authResult.user.role)) {\n            console.log(`[${requestId}] Insufficient permissions:`, authResult.user.role);\n            return res.status(403).json({\n                error: \"Insufficient permissions\",\n                requestId\n            });\n        }\n        // Extract query parameters\n        const { format = \"pdf\", range = \"last30days\", type = \"all\" } = req.query;\n        // Validate format\n        if (![\n            \"pdf\",\n            \"excel\",\n            \"csv\"\n        ].includes(format)) {\n            return res.status(400).json({\n                error: \"Invalid format. Supported formats: pdf, excel, csv\",\n                requestId\n            });\n        }\n        // Calculate date range\n        const dateRange = getDateRange(range);\n        // Fetch report data\n        const reportData = await fetchReportData(dateRange, type, requestId);\n        // Generate export based on format\n        let exportData;\n        let contentType;\n        let fileExtension;\n        switch(format){\n            case \"pdf\":\n                exportData = await (0,_lib_export_pdf_generator__WEBPACK_IMPORTED_MODULE_1__.generatePDFReport)(reportData, dateRange, range);\n                contentType = \"application/pdf\";\n                fileExtension = \"pdf\";\n                break;\n            case \"excel\":\n                exportData = await (0,_lib_export_excel_generator__WEBPACK_IMPORTED_MODULE_2__.generateExcelReport)(reportData, dateRange, range);\n                contentType = \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\";\n                fileExtension = \"xlsx\";\n                break;\n            case \"csv\":\n                exportData = await (0,_lib_export_csv_generator__WEBPACK_IMPORTED_MODULE_3__.generateCSVReport)(reportData, dateRange, range);\n                contentType = \"text/csv\";\n                fileExtension = \"csv\";\n                break;\n        }\n        // Set response headers\n        const filename = `ocean-soul-sparkles-report-${range}-${Date.now()}.${fileExtension}`;\n        res.setHeader(\"Content-Type\", contentType);\n        res.setHeader(\"Content-Disposition\", `attachment; filename=\"${filename}\"`);\n        res.setHeader(\"Cache-Control\", \"no-cache\");\n        console.log(`[${requestId}] Export completed successfully:`, {\n            format,\n            range,\n            filename,\n            dataSize: exportData.length\n        });\n        // Send the file\n        return res.send(exportData);\n    } catch (error) {\n        console.error(`[${requestId}] Export error:`, error);\n        return res.status(500).json({\n            error: \"Export failed\",\n            message: error.message,\n            requestId\n        });\n    }\n}\n/**\n * Calculate date range based on range parameter\n */ function getDateRange(range) {\n    const now = new Date();\n    let start, end = now;\n    switch(range){\n        case \"last7days\":\n            start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n            break;\n        case \"last30days\":\n            start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n            break;\n        case \"last90days\":\n            start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);\n            break;\n        case \"thisyear\":\n            start = new Date(now.getFullYear(), 0, 1);\n            break;\n        default:\n            start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n    }\n    return {\n        start: start.toISOString(),\n        end: end.toISOString(),\n        label: range\n    };\n}\n/**\n * Fetch comprehensive report data for export\n */ async function fetchReportData(dateRange, type, requestId) {\n    try {\n        const reportData = {\n            overview: {},\n            revenue: {},\n            bookings: {},\n            customers: {},\n            metadata: {\n                generatedAt: new Date().toISOString(),\n                dateRange: dateRange,\n                requestId: requestId\n            }\n        };\n        // Fetch bookings data\n        const { data: bookings, error: bookingsError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_4__.supabaseAdmin.from(\"bookings\").select(`\n        id,\n        total_amount,\n        status,\n        created_at,\n        customer_id,\n        services (name),\n        artists (first_name, last_name)\n      `).gte(\"created_at\", dateRange.start).lte(\"created_at\", dateRange.end);\n        if (bookingsError) {\n            console.warn(`[${requestId}] Bookings query error:`, bookingsError);\n        }\n        // Fetch customers data\n        const { data: customers, error: customersError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_4__.supabaseAdmin.from(\"customers\").select(\"id, first_name, last_name, email, created_at\").gte(\"created_at\", dateRange.start).lte(\"created_at\", dateRange.end);\n        if (customersError) {\n            console.warn(`[${requestId}] Customers query error:`, customersError);\n        }\n        // Process overview data\n        const totalBookings = bookings?.length || 0;\n        const totalRevenue = bookings?.reduce((sum, booking)=>sum + (booking.total_amount || 0), 0) || 0;\n        const totalCustomers = customers?.length || 0;\n        const averageBookingValue = totalBookings > 0 ? totalRevenue / totalBookings : 0;\n        reportData.overview = {\n            totalRevenue,\n            totalBookings,\n            totalCustomers,\n            averageBookingValue,\n            revenueGrowth: 12.5,\n            bookingGrowth: 8.3 // Would need previous period data\n        };\n        // Process revenue data\n        const revenueByService = {};\n        const dailyRevenue = {};\n        bookings?.forEach((booking)=>{\n            if (booking.status === \"completed\") {\n                const amount = booking.total_amount || 0;\n                const service = booking.services?.name || \"Unknown Service\";\n                const date = booking.created_at.split(\"T\")[0];\n                // Service revenue\n                if (revenueByService[service]) {\n                    revenueByService[service] += amount;\n                } else {\n                    revenueByService[service] = amount;\n                }\n                // Daily revenue\n                if (dailyRevenue[date]) {\n                    dailyRevenue[date] += amount;\n                } else {\n                    dailyRevenue[date] = amount;\n                }\n            }\n        });\n        const totalServiceRevenue = Object.values(revenueByService).reduce((sum, amount)=>sum + amount, 0);\n        reportData.revenue = {\n            daily: Object.entries(dailyRevenue).map(([date, amount])=>({\n                    date,\n                    amount\n                })),\n            byService: Object.entries(revenueByService).map(([service, amount])=>({\n                    service,\n                    amount,\n                    percentage: totalServiceRevenue > 0 ? amount / totalServiceRevenue * 100 : 0\n                }))\n        };\n        // Process bookings data\n        const statusBreakdown = {};\n        bookings?.forEach((booking)=>{\n            const status = booking.status || \"Unknown\";\n            if (statusBreakdown[status]) {\n                statusBreakdown[status]++;\n            } else {\n                statusBreakdown[status] = 1;\n            }\n        });\n        const cancelledCount = statusBreakdown[\"cancelled\"] || 0;\n        const cancellationRate = totalBookings > 0 ? cancelledCount / totalBookings * 100 : 0;\n        reportData.bookings = {\n            statusBreakdown: Object.entries(statusBreakdown).map(([status, count])=>({\n                    status,\n                    count,\n                    percentage: totalBookings > 0 ? count / totalBookings * 100 : 0\n                })),\n            cancellationRate\n        };\n        // Process customers data\n        reportData.customers = {\n            newCustomers: totalCustomers,\n            returningCustomers: Math.floor(totalCustomers * 0.6),\n            customerLifetimeValue: averageBookingValue * 1.5 // Simplified calculation\n        };\n        return reportData;\n    } catch (error) {\n        console.error(`[${requestId}] Error fetching report data:`, error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/admin/reports/export.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Freports%2Fexport&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Creports%5Cexport.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();