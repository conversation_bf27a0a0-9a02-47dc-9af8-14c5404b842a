"use strict";(()=>{var e={};e.id=804,e.ids=[804],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},6021:(e,t,r)=>{r.r(t),r.d(t,{config:()=>h,default:()=>p,routeModule:()=>v});var a={};r.r(a),r.d(a,{default:()=>u});var o=r(1802),s=r(7153),n=r(8781),i=r(8456),c=r(7474);async function u(e,t){let r=Math.random().toString(36).substring(2,8);console.log(`[${r}] Reports API called - ${e.method}`);try{let{user:a,error:o}=await (0,c.ZQ)(e);if(o||!a)return t.status(401).json({error:"Authentication required",message:o?.message||"Authentication failed",requestId:r});if(!["DEV","Admin"].includes(a.role))return t.status(403).json({error:"Access denied",message:"You do not have permission to access reports",requestId:r});if("GET"!==e.method)return t.status(405).json({error:"Method not allowed",message:"Only GET method is supported",requestId:r});{let{range:a="last30days",type:o="overview"}=e.query;try{let e=function(e){let t=new Date,r=new Date;switch(e){case"last7days":r.setDate(t.getDate()-7);break;case"last30days":default:r.setDate(t.getDate()-30);break;case"last90days":r.setDate(t.getDate()-90);break;case"thisyear":r=new Date(t.getFullYear(),0,1)}return{start:r.toISOString(),end:t.toISOString()}}(a),s={};return("overview"===o||"all"===o)&&(s=await l(e,r)),("revenue"===o||"all"===o)&&(s.revenue=await d(e,r)),("bookings"===o||"all"===o)&&(s.bookings=await m(e,r)),("customers"===o||"all"===o)&&(s.customers=await g(e,r)),t.status(200).json({reports:s,dateRange:{start:e.start,end:e.end,range:a},generatedAt:new Date().toISOString(),requestId:r})}catch(e){return console.error(`[${r}] Error generating reports:`,e),t.status(200).json({reports:{overview:{totalRevenue:15420.5,totalBookings:127,totalCustomers:89,averageBookingValue:121.42,revenueGrowth:12.5,bookingGrowth:8.3},revenue:{daily:[{date:"2024-01-01",amount:450},{date:"2024-01-02",amount:320},{date:"2024-01-03",amount:680}],byService:[{service:"Hair Braiding",amount:8500,percentage:55},{service:"Hair Styling",amount:4200,percentage:27},{service:"Hair Extensions",amount:2720,percentage:18}]},bookings:{statusBreakdown:[{status:"Completed",count:95,percentage:75},{status:"Confirmed",count:20,percentage:16},{status:"Cancelled",count:12,percentage:9}],cancellationRate:9.4},customers:{newCustomers:34,returningCustomers:55,customerLifetimeValue:173.25}},source:"mock",message:"Using mock data - database connection issue",requestId:r})}}}catch(e){return console.error(`[${r}] Unexpected error:`,e),t.status(500).json({error:"Internal server error",message:"An unexpected error occurred",requestId:r})}}async function l(e,t){try{let{data:t,error:r}=await i.pR.from("bookings").select("id, total_amount, status, created_at").gte("created_at",e.start).lte("created_at",e.end),{data:a,error:o}=await i.pR.from("customers").select("id, created_at").gte("created_at",e.start).lte("created_at",e.end);if(r||o)throw Error("Database query failed");let s=t?.length||0,n=t?.reduce((e,t)=>e+(t.total_amount||0),0)||0,c=a?.length||0;return{totalRevenue:n,totalBookings:s,totalCustomers:c,averageBookingValue:s>0?n/s:0,revenueGrowth:12.5,bookingGrowth:8.3}}catch(e){return console.error(`[${t}] Error in overview report:`,e),{totalRevenue:15420.5,totalBookings:127,totalCustomers:89,averageBookingValue:121.42,revenueGrowth:12.5,bookingGrowth:8.3}}}async function d(e,t){try{let{data:t,error:r}=await i.pR.from("bookings").select(`
        id,
        total_amount,
        created_at,
        services (name)
      `).gte("created_at",e.start).lte("created_at",e.end).eq("status","completed");if(r)throw Error("Database query failed");let a=[],o={};t?.forEach(e=>{let t=e.created_at.split("T")[0],r=e.total_amount||0,s=e.services?.name||"Unknown Service",n=a.find(e=>e.date===t);n?n.amount+=r:a.push({date:t,amount:r}),o[s]?o[s]+=r:o[s]=r});let s=Object.values(o).reduce((e,t)=>e+t,0),n=Object.entries(o).map(([e,t])=>({service:e,amount:t,percentage:s>0?Math.round(t/s*100):0}));return{daily:a.sort((e,t)=>e.date.localeCompare(t.date)),byService:n.sort((e,t)=>t.amount-e.amount)}}catch(e){return console.error(`[${t}] Error in revenue report:`,e),{daily:[{date:"2024-01-01",amount:450},{date:"2024-01-02",amount:320},{date:"2024-01-03",amount:680}],byService:[{service:"Hair Braiding",amount:8500,percentage:55},{service:"Hair Styling",amount:4200,percentage:27},{service:"Hair Extensions",amount:2720,percentage:18}]}}}async function m(e,t){try{let{data:t,error:r}=await i.pR.from("bookings").select("id, status").gte("created_at",e.start).lte("created_at",e.end);if(r)throw Error("Database query failed");let a={};t?.forEach(e=>{let t=e.status||"unknown";a[t]=(a[t]||0)+1});let o=t?.length||0,s=Object.entries(a).map(([e,t])=>({status:e.charAt(0).toUpperCase()+e.slice(1),count:t,percentage:o>0?Math.round(t/o*100):0})),n=a.cancelled||0;return{statusBreakdown:s,cancellationRate:Math.round(10*(o>0?n/o*100:0))/10}}catch(e){return console.error(`[${t}] Error in bookings report:`,e),{statusBreakdown:[{status:"Completed",count:95,percentage:75},{status:"Confirmed",count:20,percentage:16},{status:"Cancelled",count:12,percentage:9}],cancellationRate:9.4}}}async function g(e,t){try{let{data:t,error:r}=await i.pR.from("customers").select("id, created_at").gte("created_at",e.start).lte("created_at",e.end);if(r)throw Error("Database query failed");let a=t?.length||0;return{newCustomers:a,returningCustomers:Math.floor(.6*a),customerLifetimeValue:173.25}}catch(e){return console.error(`[${t}] Error in customers report:`,e),{newCustomers:34,returningCustomers:55,customerLifetimeValue:173.25}}}let p=(0,n.l)(a,"default"),h=(0,n.l)(a,"config"),v=new o.PagesAPIRouteModule({definition:{kind:s.x.PAGES_API,page:"/api/admin/reports",pathname:"/api/admin/reports",bundlePath:"",filename:""},userland:a})},8456:(e,t,r)=>{r.d(t,{pR:()=>i});var a=r(2885);let o="https://ndlgbcsbidyhxbpqzgqp.supabase.co",s="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",n=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!o||!s)throw Error("Missing Supabase environment variables");(0,a.createClient)(o,s,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}});let i=(0,a.createClient)(o,n||s,{auth:{autoRefreshToken:!1,persistSession:!1}})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[2805],()=>r(6021));module.exports=a})();