"use strict";(()=>{var e={};e.id=8766,e.ids=[8766],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},5668:(e,r,t)=>{t.r(r),t.d(r,{config:()=>v,default:()=>p,routeModule:()=>f});var s={};t.r(s),t.d(s,{default:()=>c});var i=t(1802),o=t(7153),a=t(8781),n=t(7474),u=t(2885);let d=process.env.SUPABASE_SERVICE_ROLE_KEY,l=(0,u.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",d);async function c(e,r){let{id:t}=e.query;if(!t||"string"!=typeof t)return r.status(400).json({error:"Service ID is required"});try{let s=e.headers.authorization?.replace("Bearer ","")||e.cookies["admin-token"];if(!s)return r.status(401).json({error:"No authentication token"});let i=await (0,n.Wg)(s);if(!i.valid||!i.user)return r.status(401).json({error:"Invalid authentication"});let o=i.user;if("Admin"!==o.role&&"DEV"!==o.role)return r.status(403).json({error:"Insufficient permissions"});if("GET"===e.method){let{data:e,error:s}=await l.from("services").select(`
          id,
          name,
          description,
          duration,
          price,
          category,
          status,
          visible_on_public,
          visible_on_pos,
          visible_on_events,
          created_at,
          updated_at
        `).eq("id",t).single();if(s)return console.error("Service query error:",s),r.status(500).json({error:"Failed to fetch service"});if(!e)return r.status(404).json({error:"Service not found"});let{count:i}=await l.from("bookings").select("*",{count:"exact",head:!0}).eq("service_id",t),o={...e,is_active:"active"===e.status,total_bookings:i||0};return r.status(200).json({service:o})}if("PUT"===e.method){let{name:s,description:i,duration:o,price:a,category:n,status:u,visible_on_public:d,visible_on_pos:c,visible_on_events:p}=e.body;if(!s||!n)return r.status(400).json({error:"Name and category are required"});let{data:v,error:f}=await l.from("services").update({name:s,description:i,duration:o?parseInt(o):null,price:a?parseFloat(a):null,category:n,status:u||"active",visible_on_public:!1!==d,visible_on_pos:!1!==c,visible_on_events:!1!==p,updated_at:new Date().toISOString()}).eq("id",t).select().single();if(f)return console.error("Service update error:",f),r.status(500).json({error:"Failed to update service"});return r.status(200).json({service:{...v,is_active:"active"===v.status}})}{if("DELETE"!==e.method)return r.status(405).json({error:"Method not allowed"});let{error:s}=await l.from("services").delete().eq("id",t);if(s)return console.error("Service delete error:",s),r.status(500).json({error:"Failed to delete service"});return r.status(200).json({message:"Service deleted successfully"})}}catch(e){return console.error("Services API error:",e),r.status(500).json({error:"Internal server error"})}}let p=(0,a.l)(s,"default"),v=(0,a.l)(s,"config"),f=new i.PagesAPIRouteModule({definition:{kind:o.x.PAGES_API,page:"/api/admin/services/[id]",pathname:"/api/admin/services/[id]",bundlePath:"",filename:""},userland:s})}};var r=require("../../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[2805],()=>t(5668));module.exports=s})();