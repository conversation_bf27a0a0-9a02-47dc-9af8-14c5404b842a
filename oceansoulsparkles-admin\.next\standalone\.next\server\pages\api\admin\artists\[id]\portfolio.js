"use strict";(()=>{var e={};e.id=6127,e.ids=[6127],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5828:e=>{e.exports=require("uuid")},8781:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},8810:(e,t,r)=>{r.r(t),r.d(t,{config:()=>p,default:()=>m,routeModule:()=>c});var o={};r.r(o),r.d(o,{default:()=>f});var i=r(1802),s=r(7153),a=r(8781),n=r(2885),l=r(5828);let d=process.env.SUPABASE_SERVICE_ROLE_KEY,u=(0,n.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",d);async function f(e,t){let r=(0,l.v4)(),{id:o}=e.query;try{let i=e.headers.authorization;if(!i||!i.startsWith("Bearer "))return t.status(401).json({error:"Unauthorized",message:"Valid authentication token required",requestId:r});let{data:s,error:a}=await u.from("artist_profiles").select("id, name, email").eq("id",o).single();if(a||!s)return t.status(404).json({error:"Artist not found",message:"The specified artist does not exist",requestId:r});if("GET"===e.method){let{category:i,is_featured:a,is_public:n,limit:l=50,offset:d=0}=e.query,f=u.from("artist_portfolio_items").select(`
          id,
          artist_id,
          title,
          description,
          category,
          image_url,
          thumbnail_url,
          tags,
          is_featured,
          is_public,
          display_order,
          work_date,
          customer_consent,
          created_at,
          updated_at
        `).eq("artist_id",o).order("display_order",{ascending:!0}).order("created_at",{ascending:!1});i&&(f=f.eq("category",i)),void 0!==a&&(f=f.eq("is_featured","true"===a)),void 0!==n&&(f=f.eq("is_public","true"===n)),f=f.range(parseInt(d),parseInt(d)+parseInt(l)-1);let{data:m,error:p}=await f;if(p)return console.error("Portfolio fetch error:",p),t.status(500).json({error:"Database error",message:"Failed to fetch portfolio items",requestId:r});let{count:c}=await u.from("artist_portfolio_items").select("*",{count:"exact",head:!0}).eq("artist_id",o),_=new Set(m?.map(e=>e.category)||[]),g={totalItems:m?.length||0,featuredItems:m?.filter(e=>e.is_featured).length||0,publicItems:m?.filter(e=>e.is_public).length||0,categories:Array.from(_),lastUpdated:m?.[0]?.updated_at||null};return t.status(200).json({artist:s,portfolioItems:m||[],stats:g,pagination:{total:c||0,limit:parseInt(l),offset:parseInt(d),hasMore:parseInt(d)+parseInt(l)<(c||0)},requestId:r})}if("POST"===e.method){let i=e.body;if(!i.title||!i.category||!i.image_url)return t.status(400).json({error:"Validation error",message:"Missing required fields: title, category, image_url",requestId:r});let s=i.display_order||0;if(!i.display_order){let{data:e}=await u.from("artist_portfolio_items").select("display_order").eq("artist_id",o).order("display_order",{ascending:!1}).limit(1);e&&e.length>0&&(s=(e[0].display_order||0)+1)}let a={id:(0,l.v4)(),artist_id:o,title:i.title,description:i.description||null,category:i.category,image_url:i.image_url,thumbnail_url:i.thumbnail_url||null,tags:i.tags||[],is_featured:i.is_featured||!1,is_public:!1!==i.is_public,display_order:s,work_date:i.work_date||null,customer_consent:i.customer_consent||!1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:n,error:d}=await u.from("artist_portfolio_items").insert([a]).select().single();if(d)return console.error("Portfolio creation error:",d),t.status(500).json({error:"Database error",message:"Failed to create portfolio item",requestId:r});return t.status(201).json({portfolioItem:n,message:"Portfolio item created successfully",requestId:r})}if("PUT"===e.method){let{item_id:i}=e.query,s=e.body;if(!i)return t.status(400).json({error:"Validation error",message:"Portfolio item ID is required",requestId:r});let{data:a,error:n}=await u.from("artist_portfolio_items").select("*").eq("id",i).eq("artist_id",o).single();if(n||!a)return t.status(404).json({error:"Portfolio item not found",message:"The specified portfolio item does not exist for this artist",requestId:r});let l={...s,updated_at:new Date().toISOString()},{data:d,error:f}=await u.from("artist_portfolio_items").update(l).eq("id",i).eq("artist_id",o).select().single();if(f)return console.error("Portfolio update error:",f),t.status(500).json({error:"Database error",message:"Failed to update portfolio item",requestId:r});return t.status(200).json({portfolioItem:d,message:"Portfolio item updated successfully",requestId:r})}if("DELETE"===e.method){let{item_id:i}=e.query;if(!i)return t.status(400).json({error:"Validation error",message:"Portfolio item ID is required",requestId:r});let{data:s,error:a}=await u.from("artist_portfolio_items").select("id, title").eq("id",i).eq("artist_id",o).single();if(a||!s)return t.status(404).json({error:"Portfolio item not found",message:"The specified portfolio item does not exist for this artist",requestId:r});let{error:n}=await u.from("artist_portfolio_items").delete().eq("id",i).eq("artist_id",o);if(n)return console.error("Portfolio deletion error:",n),t.status(500).json({error:"Database error",message:"Failed to delete portfolio item",requestId:r});return t.status(200).json({message:"Portfolio item deleted successfully",deletedItem:s,requestId:r})}return t.status(405).json({error:"Method not allowed",message:`HTTP method ${e.method} is not supported`,requestId:r})}catch(e){return console.error("Portfolio API error:",e),t.status(500).json({error:"Internal server error",message:"An unexpected error occurred",requestId:r})}}let m=(0,a.l)(o,"default"),p=(0,a.l)(o,"config"),c=new i.PagesAPIRouteModule({definition:{kind:s.x.PAGES_API,page:"/api/admin/artists/[id]/portfolio",pathname:"/api/admin/artists/[id]/portfolio",bundlePath:"",filename:""},userland:o})},7153:(e,t)=>{var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},1802:(e,t,r)=>{e.exports=r(1287)}};var t=require("../../../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=8810);module.exports=r})();