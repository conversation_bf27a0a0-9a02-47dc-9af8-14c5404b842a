"use strict";(()=>{var e={};e.id=6127,e.ids=[6127],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6555:e=>{e.exports=import("uuid")},8781:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},9677:(e,t,r)=>{r.a(e,async(e,o)=>{try{r.r(t),r.d(t,{config:()=>u,default:()=>d,routeModule:()=>f});var i=r(1802),s=r(7153),a=r(8781),n=r(364),l=e([n]);n=(l.then?(await l)():l)[0];let d=(0,a.l)(n,"default"),u=(0,a.l)(n,"config"),f=new i.PagesAPIRouteModule({definition:{kind:s.x.PAGES_API,page:"/api/admin/artists/[id]/portfolio",pathname:"/api/admin/artists/[id]/portfolio",bundlePath:"",filename:""},userland:n});o()}catch(e){o(e)}})},364:(e,t,r)=>{r.a(e,async(e,o)=>{try{r.r(t),r.d(t,{default:()=>n});var i=r(2885),s=r(6555),a=e([s]);s=(a.then?(await a)():a)[0];let l=process.env.SUPABASE_SERVICE_ROLE_KEY,d=(0,i.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",l);async function n(e,t){let r=(0,s.v4)(),{id:o}=e.query;try{let i=e.headers.authorization;if(!i||!i.startsWith("Bearer "))return t.status(401).json({error:"Unauthorized",message:"Valid authentication token required",requestId:r});let{data:a,error:n}=await d.from("artist_profiles").select("id, name, email").eq("id",o).single();if(n||!a)return t.status(404).json({error:"Artist not found",message:"The specified artist does not exist",requestId:r});if("GET"===e.method){let{category:i,is_featured:s,is_public:n,limit:l=50,offset:u=0}=e.query,f=d.from("artist_portfolio_items").select(`
          id,
          artist_id,
          title,
          description,
          category,
          image_url,
          thumbnail_url,
          tags,
          is_featured,
          is_public,
          display_order,
          work_date,
          customer_consent,
          created_at,
          updated_at
        `).eq("artist_id",o).order("display_order",{ascending:!0}).order("created_at",{ascending:!1});i&&(f=f.eq("category",i)),void 0!==s&&(f=f.eq("is_featured","true"===s)),void 0!==n&&(f=f.eq("is_public","true"===n)),f=f.range(parseInt(u),parseInt(u)+parseInt(l)-1);let{data:c,error:m}=await f;if(m)return console.error("Portfolio fetch error:",m),t.status(500).json({error:"Database error",message:"Failed to fetch portfolio items",requestId:r});let{count:p}=await d.from("artist_portfolio_items").select("*",{count:"exact",head:!0}).eq("artist_id",o),_=new Set(c?.map(e=>e.category)||[]),g={totalItems:c?.length||0,featuredItems:c?.filter(e=>e.is_featured).length||0,publicItems:c?.filter(e=>e.is_public).length||0,categories:Array.from(_),lastUpdated:c?.[0]?.updated_at||null};return t.status(200).json({artist:a,portfolioItems:c||[],stats:g,pagination:{total:p||0,limit:parseInt(l),offset:parseInt(u),hasMore:parseInt(u)+parseInt(l)<(p||0)},requestId:r})}if("POST"===e.method){let i=e.body;if(!i.title||!i.category||!i.image_url)return t.status(400).json({error:"Validation error",message:"Missing required fields: title, category, image_url",requestId:r});let a=i.display_order||0;if(!i.display_order){let{data:e}=await d.from("artist_portfolio_items").select("display_order").eq("artist_id",o).order("display_order",{ascending:!1}).limit(1);e&&e.length>0&&(a=(e[0].display_order||0)+1)}let n={id:(0,s.v4)(),artist_id:o,title:i.title,description:i.description||null,category:i.category,image_url:i.image_url,thumbnail_url:i.thumbnail_url||null,tags:i.tags||[],is_featured:i.is_featured||!1,is_public:!1!==i.is_public,display_order:a,work_date:i.work_date||null,customer_consent:i.customer_consent||!1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:l,error:u}=await d.from("artist_portfolio_items").insert([n]).select().single();if(u)return console.error("Portfolio creation error:",u),t.status(500).json({error:"Database error",message:"Failed to create portfolio item",requestId:r});return t.status(201).json({portfolioItem:l,message:"Portfolio item created successfully",requestId:r})}if("PUT"===e.method){let{item_id:i}=e.query,s=e.body;if(!i)return t.status(400).json({error:"Validation error",message:"Portfolio item ID is required",requestId:r});let{data:a,error:n}=await d.from("artist_portfolio_items").select("*").eq("id",i).eq("artist_id",o).single();if(n||!a)return t.status(404).json({error:"Portfolio item not found",message:"The specified portfolio item does not exist for this artist",requestId:r});let l={...s,updated_at:new Date().toISOString()},{data:u,error:f}=await d.from("artist_portfolio_items").update(l).eq("id",i).eq("artist_id",o).select().single();if(f)return console.error("Portfolio update error:",f),t.status(500).json({error:"Database error",message:"Failed to update portfolio item",requestId:r});return t.status(200).json({portfolioItem:u,message:"Portfolio item updated successfully",requestId:r})}if("DELETE"===e.method){let{item_id:i}=e.query;if(!i)return t.status(400).json({error:"Validation error",message:"Portfolio item ID is required",requestId:r});let{data:s,error:a}=await d.from("artist_portfolio_items").select("id, title").eq("id",i).eq("artist_id",o).single();if(a||!s)return t.status(404).json({error:"Portfolio item not found",message:"The specified portfolio item does not exist for this artist",requestId:r});let{error:n}=await d.from("artist_portfolio_items").delete().eq("id",i).eq("artist_id",o);if(n)return console.error("Portfolio deletion error:",n),t.status(500).json({error:"Database error",message:"Failed to delete portfolio item",requestId:r});return t.status(200).json({message:"Portfolio item deleted successfully",deletedItem:s,requestId:r})}return t.status(405).json({error:"Method not allowed",message:`HTTP method ${e.method} is not supported`,requestId:r})}catch(e){return console.error("Portfolio API error:",e),t.status(500).json({error:"Internal server error",message:"An unexpected error occurred",requestId:r})}}o()}catch(e){o(e)}})},7153:(e,t)=>{var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},1802:(e,t,r)=>{e.exports=r(1287)}};var t=require("../../../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=9677);module.exports=r})();