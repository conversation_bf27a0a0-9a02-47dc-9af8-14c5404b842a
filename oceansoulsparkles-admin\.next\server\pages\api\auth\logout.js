"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/logout";
exports.ids = ["pages/api/auth/logout"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("bcryptjs");

/***/ }),

/***/ "jsonwebtoken":
/*!*******************************!*\
  !*** external "jsonwebtoken" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("jsonwebtoken");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "speakeasy":
/*!****************************!*\
  !*** external "speakeasy" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("speakeasy");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogout&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cauth%5Clogout.ts&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogout&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cauth%5Clogout.ts&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_auth_logout_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\auth\\logout.ts */ \"(api)/./pages/api/auth/logout.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_auth_logout_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_auth_logout_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/logout\",\n        pathname: \"/api/auth/logout\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_auth_logout_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogout&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cauth%5Clogout.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/auth/admin-auth.ts":
/*!********************************!*\
  !*** ./lib/auth/admin-auth.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminLogin: () => (/* binding */ adminLogin),\n/* harmony export */   adminLogout: () => (/* binding */ adminLogout),\n/* harmony export */   authenticateAdminRequest: () => (/* binding */ authenticateAdminRequest),\n/* harmony export */   enableMFA: () => (/* binding */ enableMFA),\n/* harmony export */   generateMFASecret: () => (/* binding */ generateMFASecret),\n/* harmony export */   verifyAdminAuth: () => (/* binding */ verifyAdminAuth),\n/* harmony export */   verifyAdminToken: () => (/* binding */ verifyAdminToken),\n/* harmony export */   verifyMFAAndLogin: () => (/* binding */ verifyMFAAndLogin)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"jsonwebtoken\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _security_audit_logging__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../security/audit-logging */ \"(api)/./lib/security/audit-logging.ts\");\n\n\n\n\n// Handle missing environment variables gracefully\nconst supabaseUrl = \"https://ndlgbcsbidyhxbpqzgqp.supabase.co\" || 0;\nconst supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(supabaseUrl, supabaseKey);\n/**\r\n * Verify admin authentication token\r\n */ async function verifyAdminToken(token) {\n    try {\n        // Handle missing JWT secret gracefully\n        const jwtSecret = process.env.JWT_SECRET || \"placeholder-secret\";\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, jwtSecret);\n        // Get user from database with latest info\n        const { data: user, error } = await supabase.from(\"admin_users\").select(`\r\n        id,\r\n        email,\r\n        role,\r\n        first_name,\r\n        last_name,\r\n        is_active,\r\n        mfa_enabled,\r\n        last_activity,\r\n        permissions\r\n      `).eq(\"id\", decoded.userId).eq(\"is_active\", true).single();\n        if (error || !user) {\n            return {\n                valid: false,\n                error: \"User not found or inactive\"\n            };\n        }\n        // Check if user is still active\n        if (!user.is_active) {\n            return {\n                valid: false,\n                error: \"User account is deactivated\"\n            };\n        }\n        // Update last activity\n        await supabase.from(\"admin_users\").update({\n            last_activity: Date.now()\n        }).eq(\"id\", user.id);\n        return {\n            valid: true,\n            user: {\n                id: user.id,\n                email: user.email,\n                role: user.role,\n                firstName: user.first_name,\n                lastName: user.last_name,\n                isActive: user.is_active,\n                mfaEnabled: user.mfa_enabled,\n                lastActivity: Date.now(),\n                permissions: user.permissions || []\n            }\n        };\n    } catch (error) {\n        return {\n            valid: false,\n            error: \"Invalid token\"\n        };\n    }\n}\n/**\r\n * Admin login with email and password\r\n */ async function adminLogin(email, password, ip) {\n    try {\n        // Check for rate limiting\n        const { data: attempts } = await supabase.from(\"login_attempts\").select(\"*\").eq(\"email\", email).gte(\"created_at\", new Date(Date.now() - 15 * 60 * 1000).toISOString()).order(\"created_at\", {\n            ascending: false\n        });\n        if (attempts && attempts.length >= 5) {\n            await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n                action: \"LOGIN_BLOCKED\",\n                email,\n                ip,\n                reason: \"Too many failed attempts\"\n            });\n            return {\n                success: false,\n                error: \"Account temporarily locked due to too many failed attempts\"\n            };\n        }\n        // Get user from database\n        const { data: user, error } = await supabase.from(\"admin_users\").select(`\r\n        id,\r\n        email,\r\n        password_hash,\r\n        role,\r\n        first_name,\r\n        last_name,\r\n        is_active,\r\n        mfa_enabled,\r\n        mfa_secret,\r\n        permissions\r\n      `).eq(\"email\", email.toLowerCase()).single();\n        if (error || !user) {\n            await recordFailedAttempt(email, ip, error ? `Database error: ${error.message}` : \"User not found\");\n            return {\n                success: false,\n                error: \"Invalid credentials\"\n            };\n        }\n        // Check if user is active\n        if (!user.is_active) {\n            await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n                action: \"LOGIN_DENIED\",\n                userId: user.id,\n                email,\n                ip,\n                reason: \"Account deactivated\"\n            });\n            return {\n                success: false,\n                error: \"Account is deactivated\"\n            };\n        }\n        // Verify password\n        const passwordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, user.password_hash);\n        if (!passwordValid) {\n            await recordFailedAttempt(email, ip, \"Invalid password\");\n            return {\n                success: false,\n                error: \"Invalid credentials\"\n            };\n        }\n        // Clear failed attempts on successful password verification\n        await supabase.from(\"login_attempts\").delete().eq(\"email\", email);\n        // Check if MFA is required\n        if (user.mfa_enabled && user.mfa_secret) {\n            // Return success but indicate MFA is required\n            return {\n                success: true,\n                requiresMFA: true,\n                user: {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    firstName: user.first_name,\n                    lastName: user.last_name,\n                    isActive: user.is_active,\n                    mfaEnabled: user.mfa_enabled,\n                    lastActivity: Date.now(),\n                    permissions: user.permissions || []\n                }\n            };\n        }\n        // Generate JWT token\n        const jwtSecret = process.env.JWT_SECRET || \"placeholder-secret\";\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n            userId: user.id,\n            email: user.email,\n            role: user.role\n        }, jwtSecret, {\n            expiresIn: \"8h\"\n        });\n        // Update last login\n        await supabase.from(\"admin_users\").update({\n            last_login_at: new Date().toISOString(),\n            last_activity: Date.now()\n        }).eq(\"id\", user.id);\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"LOGIN_SUCCESS\",\n            userId: user.id,\n            userRole: user.role,\n            email,\n            ip\n        });\n        return {\n            success: true,\n            token,\n            user: {\n                id: user.id,\n                email: user.email,\n                role: user.role,\n                firstName: user.first_name,\n                lastName: user.last_name,\n                isActive: user.is_active,\n                mfaEnabled: user.mfa_enabled,\n                lastActivity: Date.now(),\n                permissions: user.permissions || []\n            }\n        };\n    } catch (error) {\n        console.error(\"Admin login error:\", error);\n        return {\n            success: false,\n            error: \"Login failed\"\n        };\n    }\n}\n/**\r\n * Verify MFA token and complete login\r\n * Note: This function requires Node.js runtime due to speakeasy dependency\r\n */ async function verifyMFAAndLogin(userId, mfaToken, ip) {\n    // Import speakeasy dynamically to avoid Edge Runtime issues\n    const speakeasy = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! speakeasy */ \"speakeasy\", 23));\n    try {\n        const { data: user, error } = await supabase.from(\"admin_users\").select(`\r\n        id,\r\n        email,\r\n        role,\r\n        first_name,\r\n        last_name,\r\n        is_active,\r\n        mfa_enabled,\r\n        mfa_secret,\r\n        permissions\r\n      `).eq(\"id\", userId).single();\n        if (error || !user || !user.mfa_secret) {\n            return {\n                success: false,\n                error: \"Invalid MFA setup\"\n            };\n        }\n        // Verify MFA token\n        const verified = speakeasy.totp.verify({\n            secret: user.mfa_secret,\n            encoding: \"base32\",\n            token: mfaToken,\n            window: 2\n        });\n        if (!verified) {\n            await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n                action: \"MFA_FAILED\",\n                userId: user.id,\n                email: user.email,\n                ip,\n                reason: \"Invalid MFA token\"\n            });\n            return {\n                success: false,\n                error: \"Invalid MFA token\"\n            };\n        }\n        // Generate JWT token\n        const jwtSecret = process.env.JWT_SECRET || \"placeholder-secret\";\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n            userId: user.id,\n            email: user.email,\n            role: user.role\n        }, jwtSecret, {\n            expiresIn: \"8h\"\n        });\n        // Update last login\n        await supabase.from(\"admin_users\").update({\n            last_login_at: new Date().toISOString(),\n            last_activity: Date.now()\n        }).eq(\"id\", user.id);\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"MFA_LOGIN_SUCCESS\",\n            userId: user.id,\n            userRole: user.role,\n            email: user.email,\n            ip\n        });\n        return {\n            success: true,\n            token,\n            user: {\n                id: user.id,\n                email: user.email,\n                role: user.role,\n                firstName: user.first_name,\n                lastName: user.last_name,\n                isActive: user.is_active,\n                mfaEnabled: user.mfa_enabled,\n                lastActivity: Date.now(),\n                permissions: user.permissions || []\n            }\n        };\n    } catch (error) {\n        console.error(\"MFA verification error:\", error);\n        return {\n            success: false,\n            error: \"MFA verification failed\"\n        };\n    }\n}\n/**\r\n * Generate MFA secret for user\r\n * Note: This function requires Node.js runtime due to speakeasy dependency\r\n */ async function generateMFASecret(userId) {\n    // Import speakeasy dynamically to avoid Edge Runtime issues\n    const speakeasy = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! speakeasy */ \"speakeasy\", 23));\n    try {\n        const { data: user } = await supabase.from(\"admin_users\").select(\"email, first_name, last_name\").eq(\"id\", userId).single();\n        if (!user) return null;\n        const secret = speakeasy.generateSecret({\n            name: `${user.first_name} ${user.last_name}`,\n            issuer: \"Ocean Soul Sparkles Admin\",\n            length: 32\n        });\n        return {\n            secret: secret.base32,\n            qrCode: secret.otpauth_url\n        };\n    } catch (error) {\n        console.error(\"MFA secret generation error:\", error);\n        return null;\n    }\n}\n/**\r\n * Enable MFA for user\r\n * Note: This function requires Node.js runtime due to speakeasy dependency\r\n */ async function enableMFA(userId, secret, token) {\n    // Import speakeasy dynamically to avoid Edge Runtime issues\n    const speakeasy = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! speakeasy */ \"speakeasy\", 23));\n    try {\n        // Verify the token first\n        const verified = speakeasy.totp.verify({\n            secret,\n            encoding: \"base32\",\n            token,\n            window: 2\n        });\n        if (!verified) return false;\n        // Save MFA secret to database\n        const { error } = await supabase.from(\"admin_users\").update({\n            mfa_secret: secret,\n            mfa_enabled: true\n        }).eq(\"id\", userId);\n        if (error) return false;\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"MFA_ENABLED\",\n            userId,\n            reason: \"User enabled MFA\"\n        });\n        return true;\n    } catch (error) {\n        console.error(\"MFA enable error:\", error);\n        return false;\n    }\n}\n/**\r\n * Record failed login attempt\r\n */ async function recordFailedAttempt(email, ip, reason) {\n    await supabase.from(\"login_attempts\").insert({\n        email,\n        ip_address: ip,\n        success: false,\n        reason,\n        created_at: new Date().toISOString()\n    });\n    await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n        action: \"LOGIN_FAILED\",\n        email,\n        ip,\n        reason\n    });\n}\n/**\r\n * Admin logout\r\n */ async function adminLogout(userId, ip) {\n    try {\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"LOGOUT\",\n            userId,\n            ip\n        });\n    } catch (error) {\n        console.error(\"Logout audit error:\", error);\n    }\n}\n/**\r\n * Authenticate admin request (alias for verifyAdminToken for backward compatibility)\r\n */ async function authenticateAdminRequest(token) {\n    return verifyAdminToken(token);\n}\n/**\r\n * Verify admin authentication from NextApiRequest\r\n */ async function verifyAdminAuth(req) {\n    try {\n        // Extract token from headers or cookies\n        const token = req.headers.authorization?.replace(\"Bearer \", \"\") || req.cookies[\"admin-token\"];\n        if (!token) {\n            return {\n                success: false,\n                message: \"No authentication token\"\n            };\n        }\n        const authResult = await verifyAdminToken(token);\n        if (!authResult.valid || !authResult.user) {\n            return {\n                success: false,\n                message: authResult.error || \"Invalid authentication\"\n            };\n        }\n        return {\n            success: true,\n            user: authResult.user\n        };\n    } catch (error) {\n        return {\n            success: false,\n            message: \"Authentication failed\"\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/auth/admin-auth.ts\n");

/***/ }),

/***/ "(api)/./lib/security/audit-logging.ts":
/*!***************************************!*\
  !*** ./lib/security/audit-logging.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuditActions: () => (/* binding */ AuditActions),\n/* harmony export */   auditLog: () => (/* binding */ auditLog),\n/* harmony export */   exportAuditLogs: () => (/* binding */ exportAuditLogs),\n/* harmony export */   getAuditLogs: () => (/* binding */ getAuditLogs),\n/* harmony export */   logCriticalEvent: () => (/* binding */ logCriticalEvent),\n/* harmony export */   logDataChange: () => (/* binding */ logDataChange),\n/* harmony export */   logSecurityEvent: () => (/* binding */ logSecurityEvent),\n/* harmony export */   logUserAction: () => (/* binding */ logUserAction)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\n// Handle missing environment variables gracefully\nconst supabaseUrl = \"https://ndlgbcsbidyhxbpqzgqp.supabase.co\" || 0;\nconst supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseKey);\n/**\r\n * Log audit event to database and console\r\n */ async function auditLog(entry) {\n    try {\n        const logEntry = {\n            action: entry.action,\n            user_id: entry.userId,\n            user_role: entry.userRole,\n            email: entry.email,\n            ip_address: entry.ip,\n            path: entry.path,\n            resource: entry.resource,\n            resource_id: entry.resourceId,\n            old_values: entry.oldValues,\n            new_values: entry.newValues,\n            reason: entry.reason,\n            error: entry.error,\n            metadata: entry.metadata,\n            severity: entry.severity || \"medium\",\n            created_at: new Date().toISOString()\n        };\n        // Log to database\n        const { error } = await supabase.from(\"audit_logs\").insert(logEntry);\n        if (error) {\n            console.error(\"Failed to write audit log to database:\", error);\n        }\n        // Log to console for immediate visibility\n        const logLevel = getLogLevel(entry.severity || \"medium\");\n        const logMessage = formatLogMessage(entry);\n        console[logLevel](logMessage);\n        // For critical events, also send alerts\n        if (entry.severity === \"critical\") {\n            await sendCriticalAlert(entry);\n        }\n    } catch (error) {\n        console.error(\"Audit logging failed:\", error);\n        // Fallback to console logging\n        console.error(\"AUDIT_LOG_FAILURE:\", JSON.stringify(entry, null, 2));\n    }\n}\n/**\r\n * Get appropriate console log level based on severity\r\n */ function getLogLevel(severity) {\n    switch(severity){\n        case \"low\":\n            return \"log\";\n        case \"medium\":\n            return \"log\";\n        case \"high\":\n            return \"warn\";\n        case \"critical\":\n            return \"error\";\n        default:\n            return \"log\";\n    }\n}\n/**\r\n * Format audit log message for console output\r\n */ function formatLogMessage(entry) {\n    const timestamp = new Date().toISOString();\n    const user = entry.userId ? `[User: ${entry.userId}]` : \"\";\n    const ip = entry.ip ? `[IP: ${entry.ip}]` : \"\";\n    const path = entry.path ? `[Path: ${entry.path}]` : \"\";\n    return `[AUDIT] ${timestamp} ${entry.action} ${user} ${ip} ${path} ${entry.reason || \"\"}`.trim();\n}\n/**\r\n * Send critical alert for high-severity events\r\n */ async function sendCriticalAlert(entry) {\n    try {\n        // In production, this would send alerts via:\n        // - Email to admin team\n        // - Slack/Discord webhook\n        // - SMS for critical security events\n        // - Push notifications\n        console.error(\"\\uD83D\\uDEA8 CRITICAL SECURITY EVENT:\", {\n            action: entry.action,\n            userId: entry.userId,\n            ip: entry.ip,\n            reason: entry.reason,\n            timestamp: new Date().toISOString()\n        });\n        // Example: Send email alert (implement based on your email service)\n        if (process.env.ENABLE_CRITICAL_ALERTS === \"true\") {\n            await sendEmailAlert(entry);\n        }\n    } catch (error) {\n        console.error(\"Failed to send critical alert:\", error);\n    }\n}\n/**\r\n * Send email alert for critical events\r\n */ async function sendEmailAlert(entry) {\n    // Implementation would depend on your email service\n    // This is a placeholder for the actual implementation\n    console.log(\"Email alert would be sent for:\", entry.action);\n}\n/**\r\n * Audit log helper functions for common actions\r\n */ const AuditActions = {\n    // Authentication events\n    LOGIN_SUCCESS: \"LOGIN_SUCCESS\",\n    LOGIN_FAILED: \"LOGIN_FAILED\",\n    LOGIN_BLOCKED: \"LOGIN_BLOCKED\",\n    LOGOUT: \"LOGOUT\",\n    MFA_ENABLED: \"MFA_ENABLED\",\n    MFA_DISABLED: \"MFA_DISABLED\",\n    MFA_FAILED: \"MFA_FAILED\",\n    PASSWORD_CHANGED: \"PASSWORD_CHANGED\",\n    PASSWORD_RESET: \"PASSWORD_RESET\",\n    // Access control events\n    ACCESS_GRANTED: \"ACCESS_GRANTED\",\n    ACCESS_DENIED: \"ACCESS_DENIED\",\n    UNAUTHORIZED_ACCESS: \"UNAUTHORIZED_ACCESS\",\n    INSUFFICIENT_PERMISSIONS: \"INSUFFICIENT_PERMISSIONS\",\n    SESSION_TIMEOUT: \"SESSION_TIMEOUT\",\n    // Data modification events\n    RECORD_CREATED: \"RECORD_CREATED\",\n    RECORD_UPDATED: \"RECORD_UPDATED\",\n    RECORD_DELETED: \"RECORD_DELETED\",\n    BULK_UPDATE: \"BULK_UPDATE\",\n    BULK_DELETE: \"BULK_DELETE\",\n    // Admin actions\n    USER_CREATED: \"USER_CREATED\",\n    USER_UPDATED: \"USER_UPDATED\",\n    USER_DEACTIVATED: \"USER_DEACTIVATED\",\n    ROLE_CHANGED: \"ROLE_CHANGED\",\n    PERMISSIONS_CHANGED: \"PERMISSIONS_CHANGED\",\n    // System events\n    SYSTEM_ERROR: \"SYSTEM_ERROR\",\n    CONFIGURATION_CHANGED: \"CONFIGURATION_CHANGED\",\n    BACKUP_CREATED: \"BACKUP_CREATED\",\n    BACKUP_RESTORED: \"BACKUP_RESTORED\",\n    // Security events\n    IP_BLOCKED: \"IP_BLOCKED\",\n    RATE_LIMITED: \"RATE_LIMITED\",\n    SUSPICIOUS_ACTIVITY: \"SUSPICIOUS_ACTIVITY\",\n    SECURITY_SCAN: \"SECURITY_SCAN\",\n    VULNERABILITY_DETECTED: \"VULNERABILITY_DETECTED\",\n    // Business events\n    BOOKING_CREATED: \"BOOKING_CREATED\",\n    BOOKING_MODIFIED: \"BOOKING_MODIFIED\",\n    BOOKING_CANCELLED: \"BOOKING_CANCELLED\",\n    PAYMENT_PROCESSED: \"PAYMENT_PROCESSED\",\n    REFUND_ISSUED: \"REFUND_ISSUED\"\n};\n/**\r\n * Helper function to log user actions\r\n */ async function logUserAction(action, userId, userRole, details = {}) {\n    await auditLog({\n        action,\n        userId,\n        userRole,\n        severity: \"medium\",\n        ...details\n    });\n}\n/**\r\n * Helper function to log security events\r\n */ async function logSecurityEvent(action, details = {}) {\n    await auditLog({\n        action,\n        severity: \"high\",\n        ...details\n    });\n}\n/**\r\n * Helper function to log critical security events\r\n */ async function logCriticalEvent(action, details = {}) {\n    await auditLog({\n        action,\n        severity: \"critical\",\n        ...details\n    });\n}\n/**\r\n * Helper function to log data changes\r\n */ async function logDataChange(action, userId, resource, resourceId, oldValues, newValues) {\n    await auditLog({\n        action,\n        userId,\n        resource,\n        resourceId,\n        oldValues,\n        newValues,\n        severity: \"medium\"\n    });\n}\n/**\r\n * Get audit logs with filtering and pagination\r\n */ async function getAuditLogs(filters) {\n    try {\n        let query = supabase.from(\"audit_logs\").select(\"*\").order(\"created_at\", {\n            ascending: false\n        });\n        if (filters.userId) {\n            query = query.eq(\"user_id\", filters.userId);\n        }\n        if (filters.action) {\n            query = query.eq(\"action\", filters.action);\n        }\n        if (filters.severity) {\n            query = query.eq(\"severity\", filters.severity);\n        }\n        if (filters.startDate) {\n            query = query.gte(\"created_at\", filters.startDate);\n        }\n        if (filters.endDate) {\n            query = query.lte(\"created_at\", filters.endDate);\n        }\n        if (filters.limit) {\n            query = query.limit(filters.limit);\n        }\n        if (filters.offset) {\n            query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw error;\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error(\"Error fetching audit logs:\", error);\n        return {\n            data: null,\n            error\n        };\n    }\n}\n/**\r\n * Export audit logs for compliance\r\n */ async function exportAuditLogs(startDate, endDate, format = \"json\") {\n    try {\n        const { data, error } = await supabase.from(\"audit_logs\").select(\"*\").gte(\"created_at\", startDate).lte(\"created_at\", endDate).order(\"created_at\", {\n            ascending: true\n        });\n        if (error) {\n            throw error;\n        }\n        if (format === \"csv\") {\n            return convertToCSV(data);\n        }\n        return data;\n    } catch (error) {\n        console.error(\"Error exporting audit logs:\", error);\n        throw error;\n    }\n}\n/**\r\n * Convert audit logs to CSV format\r\n */ function convertToCSV(data) {\n    if (!data || data.length === 0) {\n        return \"\";\n    }\n    const headers = Object.keys(data[0]);\n    const csvContent = [\n        headers.join(\",\"),\n        ...data.map((row)=>headers.map((header)=>{\n                const value = row[header];\n                if (typeof value === \"object\" && value !== null) {\n                    return `\"${JSON.stringify(value).replace(/\"/g, '\"\"')}\"`;\n                }\n                return `\"${String(value || \"\").replace(/\"/g, '\"\"')}\"`;\n            }).join(\",\"))\n    ].join(\"\\n\");\n    return csvContent;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/security/audit-logging.ts\n");

/***/ }),

/***/ "(api)/./pages/api/auth/logout.ts":
/*!**********************************!*\
  !*** ./pages/api/auth/logout.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_auth_admin_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/auth/admin-auth */ \"(api)/./lib/auth/admin-auth.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        // Get token from cookie or header\n        const token = req.cookies[\"admin-token\"] || req.headers.authorization?.replace(\"Bearer \", \"\");\n        if (token) {\n            // Verify token and get user info for audit logging\n            const authResult = await (0,_lib_auth_admin_auth__WEBPACK_IMPORTED_MODULE_0__.verifyAdminToken)(token);\n            if (authResult.valid && authResult.user) {\n                // Get client IP for audit logging\n                const clientIP = req.headers[\"x-forwarded-for\"] || req.headers[\"x-real-ip\"] || req.connection.remoteAddress || \"unknown\";\n                // Log the logout\n                await (0,_lib_auth_admin_auth__WEBPACK_IMPORTED_MODULE_0__.adminLogout)(authResult.user.id, clientIP);\n            }\n        }\n        // Clear the cookie\n        res.setHeader(\"Set-Cookie\", [\n            \"admin-token=; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=0\"\n        ]);\n        return res.status(200).json({\n            success: true,\n            message: \"Logged out successfully\"\n        });\n    } catch (error) {\n        console.error(\"Logout API error:\", error);\n        // Still clear the cookie even if there's an error\n        res.setHeader(\"Set-Cookie\", [\n            \"admin-token=; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=0\"\n        ]);\n        return res.status(200).json({\n            success: true,\n            message: \"Logged out\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/auth/logout.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogout&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cauth%5Clogout.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();