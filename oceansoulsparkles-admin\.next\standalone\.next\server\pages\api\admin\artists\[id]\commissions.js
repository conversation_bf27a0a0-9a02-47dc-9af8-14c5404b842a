"use strict";(()=>{var t={};t.id=2118,t.ids=[2118],t.modules={2885:t=>{t.exports=require("@supabase/supabase-js")},1287:t=>{t.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6555:t=>{t.exports=import("uuid")},8781:(t,e)=>{Object.defineProperty(e,"l",{enumerable:!0,get:function(){return function t(e,a){return a in e?e[a]:"then"in e&&"function"==typeof e.then?e.then(e=>t(e,a)):"function"==typeof e&&"default"===a?e:void 0}}})},7711:(t,e,a)=>{a.a(t,async(t,s)=>{try{a.r(e),a.d(e,{config:()=>u,default:()=>l,routeModule:()=>d});var n=a(1802),o=a(7153),r=a(8781),i=a(1669),m=t([i]);i=(m.then?(await m)():m)[0];let l=(0,r.l)(i,"default"),u=(0,r.l)(i,"config"),d=new n.PagesAPIRouteModule({definition:{kind:o.x.PAGES_API,page:"/api/admin/artists/[id]/commissions",pathname:"/api/admin/artists/[id]/commissions",bundlePath:"",filename:""},userland:i});s()}catch(t){s(t)}})},1669:(t,e,a)=>{a.a(t,async(t,s)=>{try{a.r(e),a.d(e,{default:()=>i});var n=a(2885),o=a(6555),r=t([o]);o=(r.then?(await r)():r)[0];let m=process.env.SUPABASE_SERVICE_ROLE_KEY,l=(0,n.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",m);async function i(t,e){let a=(0,o.v4)(),{id:s}=t.query;try{let n=t.headers.authorization;if(!n||!n.startsWith("Bearer "))return e.status(401).json({error:"Unauthorized",message:"Valid authentication token required",requestId:a});let{data:r,error:i}=await l.from("artist_profiles").select("id, name, email, commission_rate, total_revenue").eq("id",s).single();if(i||!r)return e.status(404).json({error:"Artist not found",message:"The specified artist does not exist",requestId:a});if("GET"===t.method){let n;let{status:o,start_date:i,end_date:m,payment_method:u,period:d="all",limit:c=50,offset:_=0}=t.query,p=l.from("commission_transactions").select(`
          id,
          artist_id,
          booking_id,
          payment_id,
          service_amount,
          commission_rate,
          commission_amount,
          tip_amount,
          total_earnings,
          status,
          payment_method,
          paid_at,
          paid_by,
          notes,
          created_at,
          updated_at,
          bookings!inner(
            id,
            start_time,
            end_time,
            status as booking_status,
            total_amount,
            customers(
              id,
              first_name,
              last_name,
              email
            ),
            services(
              id,
              name,
              category
            )
          ),
          payments(
            id,
            amount,
            method,
            status as payment_status,
            payment_time
          ),
          payer:admin_users!paid_by(
            id,
            first_name,
            last_name,
            email
          )
        `).eq("artist_id",s).order("created_at",{ascending:!1}),g=new Date;switch(d){case"week":let f=new Date(g);f.setDate(g.getDate()-g.getDay()),n=f.toISOString().split("T")[0];break;case"month":n=new Date(g.getFullYear(),g.getMonth(),1).toISOString().split("T")[0];break;case"quarter":n=new Date(g.getFullYear(),3*Math.floor(g.getMonth()/3),1).toISOString().split("T")[0];break;case"year":n=new Date(g.getFullYear(),0,1).toISOString().split("T")[0]}o&&(p=p.eq("status",o)),u&&(p=p.eq("payment_method",u)),(i||n)&&(p=p.gte("created_at",i||n)),m&&(p=p.lte("created_at",m)),p=p.range(parseInt(_),parseInt(_)+parseInt(c)-1);let{data:h,error:y}=await p;if(y)return console.error("Artist commissions fetch error:",y),e.status(500).json({error:"Database error",message:"Failed to fetch artist commission transactions",requestId:a});let{count:b}=await l.from("commission_transactions").select("*",{count:"exact",head:!0}).eq("artist_id",s),P=h||[],S={total:P.length,totalEarnings:P.reduce((t,e)=>t+(e.total_earnings||0),0),totalCommissions:P.reduce((t,e)=>t+(e.commission_amount||0),0),totalTips:P.reduce((t,e)=>t+(e.tip_amount||0),0),totalServiceAmount:P.reduce((t,e)=>t+(e.service_amount||0),0),averageCommissionRate:P.length>0?P.reduce((t,e)=>t+(e.commission_rate||0),0)/P.length:r.commission_rate||0,averageEarningsPerBooking:P.length>0?P.reduce((t,e)=>t+(e.total_earnings||0),0)/P.length:0,statusBreakdown:{pending:P.filter(t=>"pending"===t.status).length,calculated:P.filter(t=>"calculated"===t.status).length,paid:P.filter(t=>"paid"===t.status).length,disputed:P.filter(t=>"disputed"===t.status).length},paymentMethodBreakdown:{cash:P.filter(t=>"cash"===t.payment_method).length,bank_transfer:P.filter(t=>"bank_transfer"===t.payment_method).length,payroll:P.filter(t=>"payroll"===t.payment_method).length,unpaid:P.filter(t=>!t.payment_method).length}},w=new Date;w.setMonth(w.getMonth()-6);let{data:A}=await l.from("commission_transactions").select("total_earnings, commission_amount, tip_amount, created_at").eq("artist_id",s).gte("created_at",w.toISOString()).order("created_at",{ascending:!0}),I=A?.reduce((t,e)=>{let a=new Date(e.created_at).toISOString().slice(0,7);return t[a]||(t[a]={month:a,totalEarnings:0,totalCommissions:0,totalTips:0,count:0}),t[a].totalEarnings+=e.total_earnings||0,t[a].totalCommissions+=e.commission_amount||0,t[a].totalTips+=e.tip_amount||0,t[a].count+=1,t},{})||{},k=Object.values(I);return e.status(200).json({artist:r,commissions:P,stats:S,monthlyTrend:k,pagination:{total:b||0,limit:parseInt(c),offset:parseInt(_),hasMore:parseInt(_)+parseInt(c)<(b||0)},requestId:a})}if("POST"===t.method){let{auto_calculate:n=!1,booking_ids:i=[]}=t.body;if(n){let t=l.from("bookings").select(`
            id,
            total_amount,
            start_time,
            status,
            payments(
              id,
              amount,
              status,
              tip_amount
            )
          `).eq("artist_id",s).eq("status","completed").not("id","in",`(${(await l.from("commission_transactions").select("booking_id").eq("artist_id",s)).data?.map(t=>`'${t.booking_id}'`).join(",")||"''"})`);i.length>0&&(t=t.in("id",i));let{data:n,error:m}=await t;if(m)return console.error("Unpaid bookings fetch error:",m),e.status(500).json({error:"Database error",message:"Failed to fetch unpaid bookings",requestId:a});let u=[];for(let t of n||[]){let e=t.payments?.[0];if(e&&"completed"===e.status){let a=e.amount||t.total_amount||0,n=r.commission_rate||30,i=a*n/100,m=e.tip_amount||0,l=i+m;u.push({id:(0,o.v4)(),artist_id:s,booking_id:t.id,payment_id:e.id,service_amount:a,commission_rate:n,commission_amount:i,tip_amount:m,total_earnings:l,status:"calculated",created_at:new Date().toISOString(),updated_at:new Date().toISOString()})}}if(!(u.length>0))return e.status(200).json({commissions:[],message:"No unpaid bookings found for commission calculation",requestId:a});{let{data:t,error:s}=await l.from("commission_transactions").insert(u).select();if(s)return console.error("Auto-commission creation error:",s),e.status(500).json({error:"Database error",message:"Failed to create commission transactions",requestId:a});return e.status(201).json({commissions:t,message:`${u.length} commission transactions created successfully`,requestId:a})}}return e.status(400).json({error:"Invalid request",message:"Please specify auto_calculate=true to calculate commissions",requestId:a})}return e.status(405).json({error:"Method not allowed",message:`HTTP method ${t.method} is not supported`,requestId:a})}catch(t){return console.error("Artist commission API error:",t),e.status(500).json({error:"Internal server error",message:"An unexpected error occurred",requestId:a})}}s()}catch(t){s(t)}})},7153:(t,e)=>{var a;Object.defineProperty(e,"x",{enumerable:!0,get:function(){return a}}),function(t){t.PAGES="PAGES",t.PAGES_API="PAGES_API",t.APP_PAGE="APP_PAGE",t.APP_ROUTE="APP_ROUTE"}(a||(a={}))},1802:(t,e,a)=>{t.exports=a(1287)}};var e=require("../../../../../webpack-api-runtime.js");e.C(t);var a=e(e.s=7711);module.exports=a})();