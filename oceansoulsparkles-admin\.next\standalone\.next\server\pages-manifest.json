{"/_app": "pages/_app.js", "/_error": "pages/_error.js", "/admin/artists/[id]/portfolio": "pages/admin/artists/[id]/portfolio.html", "/admin/artists": "pages/admin/artists.html", "/admin/artists/portfolio": "pages/admin/artists/portfolio.html", "/admin/bookings": "pages/admin/bookings.html", "/admin/bookings/[id]": "pages/admin/bookings/[id].html", "/admin/communications": "pages/admin/communications.html", "/admin/bookings/new": "pages/admin/bookings/new.html", "/admin/customers-new": "pages/admin/customers-new.html", "/admin/customers": "pages/admin/customers.html", "/admin/customers/[id]": "pages/admin/customers/[id].html", "/admin/customers/new": "pages/admin/customers/new.html", "/admin/dashboard": "pages/admin/dashboard.html", "/admin/email-templates": "pages/admin/email-templates.html", "/admin/feedback": "pages/admin/feedback.html", "/admin/login": "pages/admin/login.html", "/admin/inventory": "pages/admin/inventory.html", "/admin/notifications": "pages/admin/notifications.html", "/admin/pos": "pages/admin/pos.html", "/admin/purchase-orders": "pages/admin/purchase-orders.html", "/admin/products": "pages/admin/products.html", "/admin/purchase-orders/new": "pages/admin/purchase-orders/new.html", "/admin/receipts": "pages/admin/receipts.html", "/admin/reports": "pages/admin/reports.html", "/admin/services": "pages/admin/services.html", "/admin/services/[id]": "pages/admin/services/[id].html", "/admin/services/[id]/edit": "pages/admin/services/[id]/edit.html", "/admin/services/new": "pages/admin/services/new.html", "/admin/settings": "pages/admin/settings.html", "/admin/sms-templates": "pages/admin/sms-templates.html", "/admin/staff": "pages/admin/staff.html", "/admin/staff/onboarding": "pages/admin/staff/onboarding.html", "/admin/staff/performance": "pages/admin/staff/performance.html", "/admin/staff/training": "pages/admin/staff/training.html", "/admin/suppliers": "pages/admin/suppliers.html", "/admin/suppliers/new": "pages/admin/suppliers/new.html", "/admin/tips": "pages/admin/tips.html", "/api/admin/artists": "pages/api/admin/artists.js", "/api/admin/artists/[id]/commissions": "pages/api/admin/artists/[id]/commissions.js", "/api/admin/artists/[id]/portfolio": "pages/api/admin/artists/[id]/portfolio.js", "/api/admin/artists/commissions": "pages/api/admin/artists/commissions.js", "/api/admin/artists/schedule": "pages/api/admin/artists/schedule.js", "/api/admin/bookings": "pages/api/admin/bookings.js", "/api/admin/bookings/[id]": "pages/api/admin/bookings/[id].js", "/api/admin/communications": "pages/api/admin/communications.js", "/api/admin/customers": "pages/api/admin/customers.js", "/api/admin/customers/[id]": "pages/api/admin/customers/[id].js", "/api/admin/customers/[id]/bookings": "pages/api/admin/customers/[id]/bookings.js", "/api/admin/dashboard": "pages/api/admin/dashboard.js", "/api/admin/email-templates": "pages/api/admin/email-templates.js", "/api/admin/email-templates/[id]": "pages/api/admin/email-templates/[id].js", "/api/admin/feedback": "pages/api/admin/feedback.js", "/api/admin/inventory": "pages/api/admin/inventory.js", "/api/admin/notifications/email": "pages/api/admin/notifications/email.js", "/api/admin/inventory/alerts": "pages/api/admin/inventory/alerts.js", "/api/admin/notifications/sms": "pages/api/admin/notifications/sms.js", "/api/admin/pos/artist-availability": "pages/api/admin/pos/artist-availability.js", "/api/admin/pos/create-booking": "pages/api/admin/pos/create-booking.js", "/api/admin/pos/process-payment": "pages/api/admin/pos/process-payment.js", "/api/admin/pos/terminal-checkout": "pages/api/admin/pos/terminal-checkout.js", "/api/admin/pos/terminal-devices": "pages/api/admin/pos/terminal-devices.js", "/api/admin/products": "pages/api/admin/products.js", "/api/admin/purchase-orders": "pages/api/admin/purchase-orders.js", "/api/admin/purchase-orders/[id]": "pages/api/admin/purchase-orders/[id].js", "/api/admin/purchase-orders/[id]/receive": "pages/api/admin/purchase-orders/[id]/receive.js", "/api/admin/receipts": "pages/api/admin/receipts.js", "/api/admin/receipts/preview": "pages/api/admin/receipts/preview.js", "/api/admin/reports": "pages/api/admin/reports.js", "/api/admin/reports/export": "pages/api/admin/reports/export.js", "/api/admin/services": "pages/api/admin/services.js", "/api/admin/services/[id]": "pages/api/admin/services/[id].js", "/api/admin/services/[id]/tiers": "pages/api/admin/services/[id]/tiers.js", "/api/admin/settings": "pages/api/admin/settings.js", "/api/admin/sms-templates": "pages/api/admin/sms-templates.js", "/api/admin/staff/onboarding": "pages/api/admin/staff/onboarding.js", "/api/admin/staff": "pages/api/admin/staff.js", "/api/admin/staff/performance": "pages/api/admin/staff/performance.js", "/api/admin/staff/schedule": "pages/api/admin/staff/schedule.js", "/api/admin/staff/training": "pages/api/admin/staff/training.js", "/api/admin/suppliers": "pages/api/admin/suppliers.js", "/api/admin/suppliers/[id]": "pages/api/admin/suppliers/[id].js", "/api/admin/tips": "pages/api/admin/tips.js", "/api/auth/login": "pages/api/auth/login.js", "/api/auth/logout": "pages/api/auth/logout.js", "/api/auth/mfa-verify": "pages/api/auth/mfa-verify.js", "/api/auth/verify": "pages/api/auth/verify.js", "/": "pages/index.html", "/api/admin/artists/portfolio": "pages/api/admin/artists/portfolio.js", "/_document": "pages/_document.js", "/404": "pages/404.html"}