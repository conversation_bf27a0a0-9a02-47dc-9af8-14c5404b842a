"use strict";(()=>{var e={};e.id=29,e.ids=[29],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},2120:(e,t,s)=>{s.r(t),s.d(t,{config:()=>m,default:()=>d,routeModule:()=>c});var a={};s.r(a),s.d(a,{default:()=>u});var r=s(1802),i=s(7153),n=s(8781),o=s(8456),l=s(7474);async function u(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});let s=Math.random().toString(36).substring(2,8);console.log(`[${s}] Artist availability API called`);try{let{user:a,error:r}=await (0,l.ZQ)(e);if(r||!a)return t.status(401).json({error:"Authentication required",message:r?.message||"Authentication failed",requestId:s});let{artist_id:i,date:n,service_duration_minutes:u}=e.query;if(!i||!n||!u)return t.status(400).json({error:"Missing required parameters",message:"artist_id, date, and service_duration_minutes are required",requestId:s});let d=parseInt(u);if(isNaN(d)||d<=0)return t.status(400).json({error:"Invalid service duration",message:"service_duration_minutes must be a positive number",requestId:s});if(!/^\d{4}-\d{2}-\d{2}$/.test(n))return t.status(400).json({error:"Invalid date format",message:"Date must be in YYYY-MM-DD format",requestId:s});console.log(`[${s}] Checking availability for artist ${i} on ${n} for ${d} minutes`);let{data:m,error:c}=await o.pR.from("artist_profiles").select("*").eq("id",i).single();if(c||!m)return t.status(404).json({error:"Artist not found",message:"The specified artist does not exist",requestId:s});if(!m.is_active)return t.status(200).json({available_slots:[],message:"Artist is currently inactive",requestId:s});let g=new Date(n+"T00:00:00Z").getDay(),{data:p,error:b}=await o.pR.from("artist_availability").select("*").eq("artist_id",i).eq("day_of_week",g).eq("is_available",!0).single();if(b||!p)return t.status(200).json({available_slots:[],message:"Artist has no availability scheduled for this day",requestId:s});let f=new Date(n+"T00:00:00Z"),_=new Date(n+"T23:59:59Z"),{data:v,error:h}=await o.pR.from("bookings").select("start_time, end_time").eq("assigned_artist_id",i).gte("start_time",f.toISOString()).lte("start_time",_.toISOString()).not("status","in",["canceled","no_show"]);if(h)return console.error(`[${s}] Error fetching bookings:`,h),t.status(500).json({error:"Failed to fetch existing bookings",details:h.message,requestId:s});let I=m.max_daily_bookings||8;if(v.length>=I)return t.status(200).json({available_slots:[],message:"Artist has reached maximum daily bookings",requestId:s});let w=(e,t)=>{if(!e)return null;let[s,a,r]=e.split(":"),i=new Date(t);return i.setUTCHours(parseInt(s,10),parseInt(a,10),parseInt(r||0,10),0),i},k=new Date(n+"T00:00:00Z"),y=w(p.start_time,k),j=w(p.end_time,k);if(!y||!j)return t.status(200).json({available_slots:[],message:"Invalid schedule times",requestId:s});let q=[],x=6e4*d,S=new Date(y);for(;S<j;){let e=new Date(S.getTime()+x);if(e>j)break;let t=v.some(t=>{let s=new Date(t.start_time),a=new Date(t.end_time),r=new Date(s.getTime()-3e5),i=new Date(a.getTime()+3e5);return S>=r&&S<i||e>r&&e<=i||S<=r&&e>=i}),s=!1;if(p.break_start_time&&p.break_end_time){let t=w(p.break_start_time,k),a=w(p.break_end_time,k);t&&a&&(s=S>=t&&S<a||e>t&&e<=a||S<=t&&e>=a)}t||s||q.push({time:S.toISOString(),status:"available"}),S=new Date(S.getTime()+9e5)}return console.log(`[${s}] Generated ${q.length} available slots`),t.status(200).json({available_slots:q,message:q.length>0?"Availability fetched successfully":"No available slots found",requestId:s})}catch(e){return console.error(`[${s}] Unexpected error:`,e),t.status(500).json({error:"Internal server error",message:"An unexpected error occurred while checking availability",requestId:s})}}let d=(0,n.l)(a,"default"),m=(0,n.l)(a,"config"),c=new r.PagesAPIRouteModule({definition:{kind:i.x.PAGES_API,page:"/api/admin/pos/artist-availability",pathname:"/api/admin/pos/artist-availability",bundlePath:"",filename:""},userland:a})},8456:(e,t,s)=>{s.d(t,{pR:()=>o});var a=s(2885);let r="https://ndlgbcsbidyhxbpqzgqp.supabase.co",i="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",n=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!r||!i)throw Error("Missing Supabase environment variables");(0,a.createClient)(r,i,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}});let o=(0,a.createClient)(r,n||i,{auth:{autoRefreshToken:!1,persistSession:!1}})}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[2805],()=>s(2120));module.exports=a})();