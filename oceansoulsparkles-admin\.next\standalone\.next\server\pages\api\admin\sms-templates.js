"use strict";(()=>{var e={};e.id=9981,e.ids=[9981],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},8411:(e,t,a)=>{a.r(t),a.d(t,{config:()=>d,default:()=>u,routeModule:()=>f});var r={};a.r(r),a.d(r,{default:()=>p});var o=a(1802),s=a(7153),n=a(8781),i=a(2885),l=a(7474),m=a(3658);let c=(0,i.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function p(e,t){let a=`sms-templates-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;try{let r=await (0,l.SA)(e);if(!r.success)return t.status(401).json({error:"Unauthorized",message:r.message,requestId:a});let{user:o}=r;if("GET"===e.method)try{let{data:e,error:r}=await c.from("sms_templates").select("*").order("category",{ascending:!0}).order("name",{ascending:!0});if(r&&"PGRST116"!==r.code)return console.error(`[${a}] Database error:`,r),t.status(500).json({error:"Failed to fetch SMS templates",message:r.message,requestId:a});let o=(0,m.getAllTemplates)();if(!e||0===e.length){let e=Object.keys(o).map(e=>({id:e,type:e,name:o[e].name,description:o[e].description,template:o[e].template,variables:o[e].variables,category:o[e].category,is_active:!0,is_default:!0}));return t.status(200).json({templates:e,source:"default",message:"Using default SMS templates",requestId:a})}let s=e.map(e=>({...e,variables:"string"==typeof e.variables?JSON.parse(e.variables):e.variables}));return t.status(200).json({templates:s,source:"database",requestId:a})}catch(e){return console.error(`[${a}] Error fetching SMS templates:`,e),t.status(500).json({error:"Failed to fetch SMS templates",message:e.message,requestId:a})}if("POST"===e.method){let{name:r,description:s,template:n,variables:i,category:l,type:m,is_active:p=!0}=e.body;if(!r||!n||!m)return t.status(400).json({error:"Missing required fields",message:"Name, template, and type are required",requestId:a});try{let{data:e,error:u}=await c.from("sms_templates").insert([{type:m,name:r,description:s||null,template:n,variables:JSON.stringify(i||[]),category:l||"custom",is_active:p,created_by:o.id,updated_by:o.id,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}]).select().single();if(u)return console.error(`[${a}] Error creating SMS template:`,u),t.status(500).json({error:"Failed to create SMS template",message:u.message,requestId:a});return t.status(201).json({template:{...e,variables:JSON.parse(e.variables)},message:"SMS template created successfully",requestId:a})}catch(e){return console.error(`[${a}] Error creating SMS template:`,e),t.status(500).json({error:"Failed to create SMS template",message:e.message,requestId:a})}}if("PUT"===e.method){let{id:r,name:s,description:n,template:i,variables:l,category:m,is_active:p}=e.body;if(!r)return t.status(400).json({error:"Template ID is required",requestId:a});try{let e={updated_by:o.id,updated_at:new Date().toISOString()};void 0!==s&&(e.name=s),void 0!==n&&(e.description=n),void 0!==i&&(e.template=i),void 0!==l&&(e.variables=JSON.stringify(l)),void 0!==m&&(e.category=m),void 0!==p&&(e.is_active=p);let{data:u,error:d}=await c.from("sms_templates").update(e).eq("id",r).select().single();if(d)return console.error(`[${a}] Error updating SMS template:`,d),t.status(500).json({error:"Failed to update SMS template",message:d.message,requestId:a});return t.status(200).json({template:{...u,variables:JSON.parse(u.variables)},message:"SMS template updated successfully",requestId:a})}catch(e){return console.error(`[${a}] Error updating SMS template:`,e),t.status(500).json({error:"Failed to update SMS template",message:e.message,requestId:a})}}if("DELETE"===e.method){let{id:r}=e.query;if(!r)return t.status(400).json({error:"Template ID is required",requestId:a});try{let{error:e}=await c.from("sms_templates").delete().eq("id",r);if(e)return console.error(`[${a}] Error deleting SMS template:`,e),t.status(500).json({error:"Failed to delete SMS template",message:e.message,requestId:a});return t.status(200).json({message:"SMS template deleted successfully",requestId:a})}catch(e){return console.error(`[${a}] Error deleting SMS template:`,e),t.status(500).json({error:"Failed to delete SMS template",message:e.message,requestId:a})}}return t.status(405).json({error:"Method not allowed",requestId:a})}catch(e){return console.error(`[${a}] SMS templates API error:`,e),t.status(500).json({error:"Internal server error",message:e.message,requestId:a})}}let u=(0,n.l)(r,"default"),d=(0,n.l)(r,"config"),f=new o.PagesAPIRouteModule({definition:{kind:s.x.PAGES_API,page:"/api/admin/sms-templates",pathname:"/api/admin/sms-templates",bundlePath:"",filename:""},userland:r})},3658:e=>{e.exports={bookingConfirmationTemplate:function(e){return`Hi ${e.customerName}! Your appointment for ${e.serviceName} is confirmed for ${e.date} at ${e.time}. Location: Ocean Soul Sparkles. Questions? Reply to this message.`},bookingReminderTemplate:function(e){let t=e.reminderHours||24,a=24===t?"tomorrow":`in ${t} hours`;return`Reminder: Your appointment for ${e.serviceName} is ${a} at ${e.time}. See you at Ocean Soul Sparkles! Reply CONFIRM to confirm or CANCEL to reschedule.`},bookingCancellationTemplate:function(e){return`Your appointment for ${e.serviceName} on ${e.date} at ${e.time} has been cancelled. To reschedule, please contact Ocean Soul Sparkles. Thank you!`},paymentReceiptTemplate:function(e){return`Payment received! $${e.amount} for ${e.serviceName}. Receipt #${e.receiptNumber}. Thank you for choosing Ocean Soul Sparkles!`},staffScheduleTemplate:function(e){return`Schedule update: You have ${e.appointmentCount} appointments on ${e.date}. Check the admin portal for details. - Ocean Soul Sparkles`},staffShiftReminderTemplate:function(e){return`Shift reminder: You're scheduled to work ${e.startTime} - ${e.endTime} on ${e.date}. See you at Ocean Soul Sparkles!`},lowInventoryAlertTemplate:function(e){let t=e.length,a=e.slice(0,3).map(e=>e.name).join(", "),r=t>3?` and ${t-3} more`:"";return`Low inventory alert: ${a}${r} need restocking. Check admin portal for details.`},customerWelcomeTemplate:function(e){return`Welcome to Ocean Soul Sparkles, ${e.name}! We're excited to help you sparkle. Book your next appointment online or reply to this message.`},promotionalTemplate:function(e){return`${e.title} at Ocean Soul Sparkles! ${e.description} Valid until ${e.expiryDate}. Book now!`},birthdayTemplate:function(e){return`Happy Birthday, ${e.name}! 🎉 Celebrate with us - enjoy 15% off your next service at Ocean Soul Sparkles. Valid for 30 days!`},appointmentFollowUpTemplate:function(e){return`Thank you for visiting Ocean Soul Sparkles! How was your ${e.serviceName} experience? We'd love your feedback. Reply or visit our website to review.`},noShowFollowUpTemplate:function(e){return`We missed you for your ${e.serviceName} appointment today. Life happens! Please contact us to reschedule. - Ocean Soul Sparkles`},waitlistNotificationTemplate:function(e){return`Great news! A spot opened up for ${e.serviceName} on ${e.date} at ${e.time}. Reply YES within 2 hours to confirm this appointment.`},serviceReminderTemplate:function(e,t){let a=t.daysSinceLastVisit||30;return`Hi ${e.name}! It's been ${a} days since your last ${t.name}. Ready for your next sparkle session? Book online or reply to schedule.`},emergencyClosureTemplate:function(e){return`Important: Ocean Soul Sparkles is temporarily closed due to ${e.reason}. We'll contact you to reschedule your appointment. Thank you for understanding.`},substituteVariables:function(e,t){let a=e;return Object.keys(t).forEach(e=>{let r=RegExp(`{{${e}}}`,"g");a=a.replace(r,t[e]||"")}),a},getAllTemplates:function(){return{booking_confirmation:{name:"Booking Confirmation",description:"Sent when a booking is confirmed",template:"Hi {{customerName}}! Your appointment for {{serviceName}} is confirmed for {{date}} at {{time}}. Location: Ocean Soul Sparkles. Questions? Reply to this message.",variables:["customerName","serviceName","date","time"],category:"booking"},booking_reminder:{name:"Booking Reminder",description:"Sent before appointment (24h default)",template:"Reminder: Your appointment for {{serviceName}} is {{timeText}} at {{time}}. See you at Ocean Soul Sparkles! Reply CONFIRM to confirm or CANCEL to reschedule.",variables:["serviceName","timeText","time"],category:"booking"},booking_cancellation:{name:"Booking Cancellation",description:"Sent when booking is cancelled",template:"Your appointment for {{serviceName}} on {{date}} at {{time}} has been cancelled. To reschedule, please contact Ocean Soul Sparkles. Thank you!",variables:["serviceName","date","time"],category:"booking"},payment_receipt:{name:"Payment Receipt",description:"Sent after successful payment",template:"Payment received! ${{amount}} for {{serviceName}}. Receipt #{{receiptNumber}}. Thank you for choosing Ocean Soul Sparkles!",variables:["amount","serviceName","receiptNumber"],category:"payment"},staff_schedule:{name:"Staff Schedule Notification",description:"Sent to staff about schedule updates",template:"Schedule update: You have {{appointmentCount}} appointments on {{date}}. Check the admin portal for details. - Ocean Soul Sparkles",variables:["appointmentCount","date"],category:"staff"},staff_shift_reminder:{name:"Staff Shift Reminder",description:"Sent to remind staff of upcoming shifts",template:"Shift reminder: You're scheduled to work {{startTime}} - {{endTime}} on {{date}}. See you at Ocean Soul Sparkles!",variables:["startTime","endTime","date"],category:"staff"},low_inventory_alert:{name:"Low Inventory Alert",description:"Sent to managers when inventory is low",template:"Low inventory alert: {{itemNames}} need restocking. Check admin portal for details.",variables:["itemNames"],category:"admin"},customer_welcome:{name:"Customer Welcome",description:"Sent to new customers",template:"Welcome to Ocean Soul Sparkles, {{name}}! We're excited to help you sparkle. Book your next appointment online or reply to this message.",variables:["name"],category:"customer"},promotional:{name:"Promotional Message",description:"Sent for promotions and special offers",template:"{{title}} at Ocean Soul Sparkles! {{description}} Valid until {{expiryDate}}. Book now!",variables:["title","description","expiryDate"],category:"marketing"},birthday:{name:"Birthday Message",description:"Sent on customer birthdays",template:"Happy Birthday, {{name}}! \uD83C\uDF89 Celebrate with us - enjoy 15% off your next service at Ocean Soul Sparkles. Valid for 30 days!",variables:["name"],category:"customer"},appointment_followup:{name:"Appointment Follow-up",description:"Sent after appointment completion",template:"Thank you for visiting Ocean Soul Sparkles! How was your {{serviceName}} experience? We'd love your feedback. Reply or visit our website to review.",variables:["serviceName"],category:"followup"},no_show_followup:{name:"No-Show Follow-up",description:"Sent when customer misses appointment",template:"We missed you for your {{serviceName}} appointment today. Life happens! Please contact us to reschedule. - Ocean Soul Sparkles",variables:["serviceName"],category:"followup"},waitlist_notification:{name:"Waitlist Notification",description:"Sent when waitlist spot becomes available",template:"Great news! A spot opened up for {{serviceName}} on {{date}} at {{time}}. Reply YES within 2 hours to confirm this appointment.",variables:["serviceName","date","time"],category:"booking"},service_reminder:{name:"Service Reminder",description:"Sent to remind customers of recurring services",template:"Hi {{name}}! It's been {{daysSince}} days since your last {{serviceName}}. Ready for your next sparkle session? Book online or reply to schedule.",variables:["name","daysSince","serviceName"],category:"customer"},emergency_closure:{name:"Emergency Closure",description:"Sent during unexpected closures",template:"Important: Ocean Soul Sparkles is temporarily closed due to {{reason}}. We'll contact you to reschedule your appointment. Thank you for understanding.",variables:["reason"],category:"admin"}}}}}};var t=require("../../../webpack-api-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[2805],()=>a(8411));module.exports=r})();