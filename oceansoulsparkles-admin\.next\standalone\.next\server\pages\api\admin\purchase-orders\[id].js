"use strict";(()=>{var e={};e.id=4434,e.ids=[4434],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},830:(e,r,s)=>{s.r(r),s.d(r,{config:()=>h,default:()=>m,routeModule:()=>f});var t={};s.r(t),s.d(t,{default:()=>u});var a=s(1802),o=s(7153),n=s(8781),d=s(7474),i=s(6482);async function u(e,r){let s=`purchase-order-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;try{let{id:t}=e.query;if(console.log(`[${s}] Individual purchase order API request:`,{method:e.method,purchaseOrderId:t,userAgent:e.headers["user-agent"]}),!t||"string"!=typeof t)return r.status(400).json({error:"Invalid purchase order ID",requestId:s});let a=await (0,d.Wg)(e);if(!a.valid)return console.log(`[${s}] Authentication failed:`,a.error),r.status(401).json({error:"Unauthorized",requestId:s});if(!["DEV","Admin"].includes(a.user.role))return console.log(`[${s}] Insufficient permissions:`,a.user.role),r.status(403).json({error:"Insufficient permissions",requestId:s});if("GET"===e.method)return await c(e,r,s,t);if("PUT"===e.method)return await l(e,r,s,t,a.user);if("DELETE"===e.method)return await p(e,r,s,t,a.user);return r.status(405).json({error:"Method not allowed",requestId:s})}catch(e){return console.error(`[${s}] Individual purchase order API error:`,e),r.status(500).json({error:"Internal server error",message:e.message,requestId:s})}}async function c(e,r,s,t){try{let{data:e,error:a}=await i.supabaseAdmin.from("purchase_orders").select(`
        *,
        suppliers (
          id,
          name,
          contact_person,
          email,
          phone,
          address,
          payment_terms,
          lead_time_days
        ),
        admin_users!purchase_orders_created_by_fkey (
          id,
          first_name,
          last_name,
          email
        )
      `).eq("id",t).single();if(a){if("PGRST116"===a.code)return r.status(404).json({error:"Purchase order not found",requestId:s});throw a}let{data:o,error:n}=await i.supabaseAdmin.from("purchase_order_items").select(`
        *,
        inventory (
          id,
          name,
          sku,
          quantity_on_hand,
          min_stock_level
        )
      `).eq("purchase_order_id",t).order("created_at");return n&&console.warn(`[${s}] Error fetching purchase order items:`,n),console.log(`[${s}] Purchase order fetched successfully:`,{id:e.id,poNumber:e.po_number,supplier:e.suppliers?.name,itemCount:o?.length||0}),r.status(200).json({purchaseOrder:{...e,items:o||[]},requestId:s})}catch(e){throw console.error(`[${s}] Error fetching purchase order:`,e),e}}async function l(e,r,s,t,a){try{let{status:o,expectedDeliveryDate:n,actualDeliveryDate:d,notes:u}=e.body,{data:c,error:l}=await i.supabaseAdmin.from("purchase_orders").select("id, po_number, status, supplier_id").eq("id",t).single();if(l){if("PGRST116"===l.code)return r.status(404).json({error:"Purchase order not found",requestId:s});throw l}let p=["draft","sent","confirmed","received","cancelled"];if(o&&!p.includes(o))return r.status(400).json({error:"Invalid status",message:`Status must be one of: ${p.join(", ")}`,requestId:s});if(["received","cancelled"].includes(c.status)&&o!==c.status)return r.status(400).json({error:"Cannot modify completed purchase order",message:"Purchase orders that are received or cancelled cannot be modified",requestId:s});let m={updated_at:new Date().toISOString()};o&&(m.status=o),n&&(m.expected_delivery_date=n),d&&(m.actual_delivery_date=d),void 0!==u&&(m.notes=u?.trim()||null);let{data:h,error:f}=await i.supabaseAdmin.from("purchase_orders").update(m).eq("id",t).select(`
        *,
        suppliers (
          id,
          name,
          contact_person,
          email
        )
      `).single();if(f)throw console.error(`[${s}] Database error updating purchase order:`,f),f;return console.log(`[${s}] Purchase order updated successfully:`,{id:h.id,poNumber:h.po_number,status:h.status,updatedBy:a.email}),r.status(200).json({purchaseOrder:h,message:"Purchase order updated successfully",requestId:s})}catch(e){throw console.error(`[${s}] Error updating purchase order:`,e),e}}async function p(e,r,s,t,a){try{let{data:e,error:o}=await i.supabaseAdmin.from("purchase_orders").select("id, po_number, status").eq("id",t).single();if(o){if("PGRST116"===o.code)return r.status(404).json({error:"Purchase order not found",requestId:s});throw o}if("draft"!==e.status)return r.status(400).json({error:"Cannot delete purchase order",message:"Only draft purchase orders can be deleted",requestId:s});let{error:n}=await i.supabaseAdmin.from("purchase_orders").delete().eq("id",t);if(n)throw console.error(`[${s}] Database error deleting purchase order:`,n),n;return console.log(`[${s}] Purchase order deleted successfully:`,{id:t,poNumber:e.po_number,deletedBy:a.email}),r.status(200).json({message:"Purchase order deleted successfully",requestId:s})}catch(e){throw console.error(`[${s}] Error deleting purchase order:`,e),e}}let m=(0,n.l)(t,"default"),h=(0,n.l)(t,"config"),f=new a.PagesAPIRouteModule({definition:{kind:o.x.PAGES_API,page:"/api/admin/purchase-orders/[id]",pathname:"/api/admin/purchase-orders/[id]",bundlePath:"",filename:""},userland:t})},6482:(e,r,s)=>{s.r(r),s.d(r,{supabaseAdmin:()=>n});var t=s(2885);let a="https://ndlgbcsbidyhxbpqzgqp.supabase.co",o=process.env.SUPABASE_SERVICE_ROLE_KEY;a&&o||console.warn("Missing Supabase environment variables for admin client");let n=(0,t.createClient)(a,o||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",{auth:{autoRefreshToken:!1,persistSession:!1}})}};var r=require("../../../../webpack-api-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[2805],()=>s(830));module.exports=t})();