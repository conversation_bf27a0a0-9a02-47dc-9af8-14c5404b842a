"use strict";(()=>{var e={};e.id=4806,e.ids=[4806],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},7191:e=>{e.exports=require("square")},6997:(e,t,r)=>{r.r(t),r.d(t,{config:()=>d,default:()=>u,routeModule:()=>l});var o={};r.r(o),r.d(o,{default:()=>i});var s=r(1802),a=r(7153),n=r(8781);r(8456);var c=r(7474);async function i(e,t){let o=Math.random().toString(36).substring(2,8);console.log(`[${o}] Square Terminal checkout API called`);try{let{user:s,error:a}=await (0,c.ZQ)(e);if(a||!s)return t.status(401).json({error:"Authentication required",message:a?.message||"Authentication failed",requestId:o});let n=process.env.SQUARE_ACCESS_TOKEN,i=process.env.SQUARE_ENVIRONMENT||"sandbox";if(!n)return t.status(500).json({error:"Square configuration missing",message:"Square Terminal integration is not properly configured",requestId:o});let{Client:u}=r(7191),d=new u({accessToken:n,environment:"production"===i?"production":"sandbox"});if("POST"===e.method){let{deviceId:r,amountMoney:s,orderDetails:a,paymentOptions:n}=e.body;if(!r||!s)return t.status(400).json({error:"Missing required fields",message:"deviceId and amountMoney are required",requestId:o});try{let e=d.terminalApi,c={idempotencyKey:`terminal_${o}_${Date.now()}`,checkout:{amountMoney:{amount:s.amount,currency:s.currency||"AUD"},deviceOptions:{deviceId:r},paymentType:"CARD_PRESENT",note:a?.service||"POS Terminal Payment",paymentOptions:{autocomplete:n?.autocomplete||!0,collectSignature:n?.collectSignature||!0,allowTipping:n?.allowTipping||!1}}};console.log(`[${o}] Creating terminal checkout:`,{deviceId:r,amount:s.amount,currency:s.currency});let{result:i,statusCode:u}=await e.createTerminalCheckout(c);if(console.log(`[${o}] Terminal checkout response:`,u),200===u&&i.checkout)return t.status(200).json({success:!0,checkout:{id:i.checkout.id,status:i.checkout.status,amountMoney:i.checkout.amountMoney,deviceId:i.checkout.deviceOptions?.deviceId,createdAt:i.checkout.createdAt,updatedAt:i.checkout.updatedAt},requestId:o});return console.error(`[${o}] Failed to create checkout:`,i),t.status(400).json({error:"Failed to create terminal checkout",message:i.errors?.[0]?.detail||"Unknown error",requestId:o})}catch(e){return console.error(`[${o}] Error creating terminal checkout:`,e),t.status(500).json({error:"Failed to create terminal checkout",message:e.message,requestId:o})}}else{if("GET"!==e.method)return t.status(405).json({error:"Method not allowed",message:"Only GET and POST methods are supported",requestId:o});let{checkoutId:r}=e.query;if(!r)return t.status(400).json({error:"Missing checkoutId",message:"checkoutId is required for status updates",requestId:o});try{let e=d.terminalApi,{result:s,statusCode:a}=await e.getTerminalCheckout(r);if(console.log(`[${o}] Terminal checkout status:`,a),200===a&&s.checkout)return t.status(200).json({success:!0,checkout:{id:s.checkout.id,status:s.checkout.status,amountMoney:s.checkout.amountMoney,paymentId:s.checkout.paymentId,deviceId:s.checkout.deviceOptions?.deviceId,createdAt:s.checkout.createdAt,updatedAt:s.checkout.updatedAt},requestId:o});return console.error(`[${o}] Failed to get checkout status:`,s),t.status(404).json({error:"Checkout not found",message:s.errors?.[0]?.detail||"Unknown error",requestId:o})}catch(e){return console.error(`[${o}] Error getting checkout status:`,e),t.status(500).json({error:"Failed to get checkout status",message:e.message,requestId:o})}}}catch(e){return console.error(`[${o}] Unexpected error:`,e),t.status(500).json({error:"Internal server error",message:"An unexpected error occurred",requestId:o})}}let u=(0,n.l)(o,"default"),d=(0,n.l)(o,"config"),l=new s.PagesAPIRouteModule({definition:{kind:a.x.PAGES_API,page:"/api/admin/pos/terminal-checkout",pathname:"/api/admin/pos/terminal-checkout",bundlePath:"",filename:""},userland:o})},8456:(e,t,r)=>{r.d(t,{pR:()=>c});var o=r(2885);let s="https://ndlgbcsbidyhxbpqzgqp.supabase.co",a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",n=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!s||!a)throw Error("Missing Supabase environment variables");(0,o.createClient)(s,a,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}});let c=(0,o.createClient)(s,n||a,{auth:{autoRefreshToken:!1,persistSession:!1}})}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[2805],()=>r(6997));module.exports=o})();