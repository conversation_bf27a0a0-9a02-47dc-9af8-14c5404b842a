"use strict";(()=>{var e={};e.id=3609,e.ids=[3609],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6555:e=>{e.exports=import("uuid")},8781:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},9995:(e,t,r)=>{r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{config:()=>u,default:()=>l,routeModule:()=>c});var a=r(1802),o=r(7153),s=r(8781),n=r(7424),d=e([n]);n=(d.then?(await d)():d)[0];let l=(0,s.l)(n,"default"),u=(0,s.l)(n,"config"),c=new a.PagesAPIRouteModule({definition:{kind:o.x.PAGES_API,page:"/api/admin/artists/portfolio",pathname:"/api/admin/artists/portfolio",bundlePath:"",filename:""},userland:n});i()}catch(e){i(e)}})},7424:(e,t,r)=>{r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{config:()=>u,default:()=>n});var a=r(2885),o=r(6555),s=e([o]);o=(s.then?(await s)():s)[0];let d=process.env.SUPABASE_SERVICE_ROLE_KEY,l=(0,a.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",d);async function n(e,t){let r=(0,o.v4)();try{let i=e.headers.authorization;if(!i||!i.startsWith("Bearer "))return t.status(401).json({error:"Unauthorized",message:"Valid authentication token required",requestId:r});if("GET"===e.method){let{artist_id:i,category:a,is_featured:o,is_public:s,limit:n=50,offset:d=0}=e.query,u=l.from("artist_portfolio_items").select(`
          id,
          artist_id,
          title,
          description,
          category,
          image_url,
          thumbnail_url,
          tags,
          is_featured,
          is_public,
          display_order,
          work_date,
          customer_consent,
          created_at,
          updated_at,
          artist_profiles!inner(
            id,
            name,
            email
          )
        `).order("display_order",{ascending:!0}).order("created_at",{ascending:!1});i&&(u=u.eq("artist_id",i)),a&&(u=u.eq("category",a)),void 0!==o&&(u=u.eq("is_featured","true"===o)),void 0!==s&&(u=u.eq("is_public","true"===s)),u=u.range(parseInt(d),parseInt(d)+parseInt(n)-1);let{data:c,error:p,count:_}=await u;if(p)return console.error("Portfolio fetch error:",p),t.status(500).json({error:"Database error",message:"Failed to fetch portfolio items",requestId:r});let{count:f}=await l.from("artist_portfolio_items").select("*",{count:"exact",head:!0});return t.status(200).json({portfolioItems:c||[],pagination:{total:f||0,limit:parseInt(n),offset:parseInt(d),hasMore:parseInt(d)+parseInt(n)<(f||0)},requestId:r})}if("POST"===e.method){let i=e.body;if(!i.artist_id||!i.title||!i.category||!i.image_url)return t.status(400).json({error:"Validation error",message:"Missing required fields: artist_id, title, category, image_url",requestId:r});let{data:a,error:s}=await l.from("artist_profiles").select("id, name").eq("id",i.artist_id).single();if(s||!a)return t.status(404).json({error:"Artist not found",message:"The specified artist does not exist",requestId:r});let n=i.display_order||0;if(!i.display_order){let{data:e}=await l.from("artist_portfolio_items").select("display_order").eq("artist_id",i.artist_id).order("display_order",{ascending:!1}).limit(1);e&&e.length>0&&(n=(e[0].display_order||0)+1)}let d={id:(0,o.v4)(),artist_id:i.artist_id,title:i.title,description:i.description||null,category:i.category,image_url:i.image_url,thumbnail_url:i.thumbnail_url||null,tags:i.tags||[],is_featured:i.is_featured||!1,is_public:!1!==i.is_public,display_order:n,work_date:i.work_date||null,customer_consent:i.customer_consent||!1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:u,error:c}=await l.from("artist_portfolio_items").insert([d]).select(`
          *,
          artist_profiles!inner(
            id,
            name,
            email
          )
        `).single();if(c)return console.error("Portfolio creation error:",c),t.status(500).json({error:"Database error",message:"Failed to create portfolio item",requestId:r});return t.status(201).json({portfolioItem:u,message:"Portfolio item created successfully",requestId:r})}return t.status(405).json({error:"Method not allowed",message:`HTTP method ${e.method} is not supported`,requestId:r})}catch(e){return console.error("Portfolio API error:",e),t.status(500).json({error:"Internal server error",message:"An unexpected error occurred",requestId:r})}}let u={api:{bodyParser:{sizeLimit:"10mb"}}};i()}catch(e){i(e)}})},7153:(e,t)=>{var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},1802:(e,t,r)=>{e.exports=r(1287)}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=9995);module.exports=r})();