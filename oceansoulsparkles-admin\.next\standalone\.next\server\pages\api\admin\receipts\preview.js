"use strict";(()=>{var e={};e.id=2849,e.ids=[2849],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},7922:(e,t,a)=>{a.r(t),a.d(t,{config:()=>f,default:()=>v,routeModule:()=>b});var s={};a.r(s),a.d(s,{default:()=>m});var i=a(1802),l=a(7153),n=a(8781),o=a(7474),r=a(8456);async function d(e,t=null){try{let a=await c(t);if(!a){console.log("No template found, using default template");let t=p(),a=u(t,e);return{success:!0,html:a,template:t}}let s=u(a,e);return{success:!0,html:s,template:a}}catch(t){console.error("Error generating receipt:",t);try{console.log("Falling back to default template due to error");let t=p(),a=u(t,e);return{success:!0,html:a,template:t}}catch(e){return console.error("Fallback template generation failed:",e),{success:!1,error:t.message}}}}async function c(e=null){try{let t=r.pR.from("receipt_templates").select("*").eq("is_active",!0);t=e?t.eq("id",e):t.eq("is_default",!0);let{data:a,error:s}=await t.limit(1);if(s){if("42P01"===s.code)return console.log("Receipt templates table not found, using default template"),p();throw Error(`Database error: ${s.message}`)}if(!a||0===a.length){let{data:e}=await r.pR.from("receipt_templates").select("*").eq("is_active",!0).limit(1);return e?.[0]||p()}return a[0]}catch(e){return console.error("Error fetching receipt template:",e),p()}}function p(){return{id:"default-standard",name:"Standard Receipt",description:"Default receipt template",template_type:"standard",is_default:!0,is_active:!0,business_name:"Ocean Soul Sparkles",business_address:"Australia",business_phone:"+61 XXX XXX XXX",business_email:"<EMAIL>",business_website:"oceansoulsparkles.com.au",business_abn:"",show_logo:!0,logo_position:"center",header_color:"#667eea",text_color:"#333333",font_family:"Arial",font_size:12,show_customer_details:!0,show_service_details:!0,show_artist_details:!0,show_payment_details:!0,show_booking_notes:!1,show_terms_conditions:!0,footer_message:"Thank you for choosing Ocean Soul Sparkles!",show_social_media:!1,social_media_links:null,custom_fields:[]}}function u(e,t){let{business_name:a,business_address:s,business_phone:i,business_email:l,business_website:n,business_abn:o,show_logo:r,logo_position:d,header_color:c,text_color:p,font_family:u,font_size:m,show_customer_details:v,show_service_details:f,show_artist_details:b,show_payment_details:h,show_booking_notes:g,show_terms_conditions:$,footer_message:w,show_social_media:x,social_media_links:_,template_type:y}=e,k=t.receipt_number||`OSS-${Date.now()}`,S=new Date().toLocaleDateString("en-AU",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),I=`
    <style>
      body { 
        font-family: ${u}, sans-serif; 
        font-size: ${m}px; 
        color: ${p}; 
        margin: 0; 
        padding: 20px; 
        line-height: 1.4;
        max-width: 400px;
        margin: 0 auto;
      }
      .receipt-header { 
        text-align: ${d}; 
        margin-bottom: 20px; 
        padding-bottom: 15px;
        border-bottom: 2px solid ${c};
      }
      .business-name { 
        font-size: ${Math.round(1.5*m)}px; 
        font-weight: bold; 
        color: ${c}; 
        margin: 0 0 5px 0;
      }
      .business-info { 
        font-size: ${Math.round(.9*m)}px; 
        color: #666; 
        margin: 2px 0;
      }
      .receipt-title { 
        font-size: ${Math.round(1.2*m)}px; 
        font-weight: bold; 
        text-align: center; 
        margin: 20px 0 15px 0;
        text-transform: uppercase;
        letter-spacing: 1px;
      }
      .receipt-info { 
        margin-bottom: 20px; 
        padding: 10px 0;
        border-bottom: 1px solid #eee;
      }
      .section { 
        margin-bottom: 15px; 
      }
      .section-title { 
        font-weight: bold; 
        margin-bottom: 8px; 
        color: ${c};
        font-size: ${Math.round(1.1*m)}px;
      }
      .detail-row { 
        display: flex; 
        justify-content: space-between; 
        margin-bottom: 3px;
        align-items: flex-start;
      }
      .detail-label { 
        font-weight: 500; 
        flex: 1;
      }
      .detail-value { 
        text-align: right; 
        flex: 1;
        word-break: break-word;
      }
      .total-section { 
        border-top: 2px solid ${c}; 
        padding-top: 10px; 
        margin-top: 15px;
      }
      .total-row { 
        display: flex; 
        justify-content: space-between; 
        font-weight: bold; 
        font-size: ${Math.round(1.1*m)}px;
        margin-bottom: 5px;
      }
      .footer { 
        text-align: center; 
        margin-top: 20px; 
        padding-top: 15px; 
        border-top: 1px solid #eee;
        font-size: ${Math.round(.9*m)}px;
      }
      .footer-message { 
        font-style: italic; 
        color: #666; 
        margin-bottom: 10px;
      }
      .compact { font-size: ${Math.round(.9*m)}px; }
      .detailed { font-size: ${m}px; }
      @media print {
        body { margin: 0; padding: 10px; }
        .receipt-header { page-break-inside: avoid; }
      }
    </style>
  `,z=`
    <div class="receipt-header">
      ${r?`<div class="business-name">${a}</div>`:""}
      ${s?`<div class="business-info">${s}</div>`:""}
      ${i?`<div class="business-info">${i}</div>`:""}
      ${l?`<div class="business-info">${l}</div>`:""}
      ${n?`<div class="business-info">${n}</div>`:""}
      ${o?`<div class="business-info">ABN: ${o}</div>`:""}
    </div>
  `,R=`
    <div class="receipt-title">Receipt</div>
    <div class="receipt-info">
      <div class="detail-row">
        <span class="detail-label">Receipt #:</span>
        <span class="detail-value">${k}</span>
      </div>
      <div class="detail-row">
        <span class="detail-label">Date:</span>
        <span class="detail-value">${S}</span>
      </div>
    </div>
  `,A="";v&&t.customer_name&&(A=`
      <div class="section">
        <div class="section-title">Customer Details</div>
        <div class="detail-row">
          <span class="detail-label">Name:</span>
          <span class="detail-value">${t.customer_name}</span>
        </div>
        ${t.customer_email?`
        <div class="detail-row">
          <span class="detail-label">Email:</span>
          <span class="detail-value">${t.customer_email}</span>
        </div>`:""}
        ${t.customer_phone?`
        <div class="detail-row">
          <span class="detail-label">Phone:</span>
          <span class="detail-value">${t.customer_phone}</span>
        </div>`:""}
      </div>
    `);let M="";if(f){let e=t.start_time?new Date(t.start_time).toLocaleString("en-AU"):"N/A",a=t.duration?`${t.duration} minutes`:"N/A";M=`
      <div class="section">
        <div class="section-title">Service Details</div>
        <div class="detail-row">
          <span class="detail-label">Service:</span>
          <span class="detail-value">${t.service_name||"N/A"}</span>
        </div>
        ${t.tier_name?`
        <div class="detail-row">
          <span class="detail-label">Tier:</span>
          <span class="detail-value">${t.tier_name}</span>
        </div>`:""}
        <div class="detail-row">
          <span class="detail-label">Date & Time:</span>
          <span class="detail-value">${e}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Duration:</span>
          <span class="detail-value">${a}</span>
        </div>
      </div>
    `}let j="";b&&t.artist_name&&(j=`
      <div class="section">
        <div class="section-title">Artist Details</div>
        <div class="detail-row">
          <span class="detail-label">Artist:</span>
          <span class="detail-value">${t.artist_name}</span>
        </div>
      </div>
    `);let q="";if(h){let e=t.total_amount||0,a=t.tip_amount||0;q=`
      <div class="section">
        <div class="section-title">Payment Details</div>
        ${a>0?`
        <div class="detail-row">
          <span class="detail-label">Subtotal:</span>
          <span class="detail-value">$${(e-a).toFixed(2)}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Tip:</span>
          <span class="detail-value">$${a.toFixed(2)}</span>
        </div>`:""}
        <div class="total-section">
          <div class="total-row">
            <span>Total:</span>
            <span>$${e.toFixed(2)}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Payment Method:</span>
            <span class="detail-value">${t.payment_method||"N/A"}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Status:</span>
            <span class="detail-value">Paid</span>
          </div>
        </div>
      </div>
    `}let P="";g&&t.notes&&(P=`
      <div class="section">
        <div class="section-title">Notes</div>
        <div>${t.notes}</div>
      </div>
    `);let D="";return(w||$)&&(D=`
      <div class="footer">
        ${w?`<div class="footer-message">${w}</div>`:""}
        ${$?`
        <div style="font-size: ${Math.round(.8*m)}px; color: #888;">
          Terms & Conditions apply. Visit our website for details.
        </div>`:""}
        ${x&&_?`
        <div style="margin-top: 10px;">
          Follow us on social media for updates and inspiration!
        </div>`:""}
      </div>
    `),`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Receipt - ${k}</title>
      ${I}
    </head>
    <body class="${y}">
      ${z}
      ${R}
      ${A}
      ${M}
      ${j}
      ${q}
      ${P}
      ${D}
    </body>
    </html>
  `}async function m(e,t){if("POST"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let a=e.headers.authorization?.replace("Bearer ","")||e.cookies["admin-token"];if(!a)return t.status(401).json({error:"No authentication token"});let s=await (0,o.ZQ)(a);if(!s.valid||!s.user)return t.status(401).json({error:"Invalid authentication"});let{templateId:i,bookingData:l}=e.body,n=Math.random().toString(36).substring(2,8);if(console.log(`[${n}] Receipt preview requested for template: ${i}`),!l)return t.status(400).json({error:"Booking data is required"});let r=await d(l,i);if(!r.success)return console.error(`[${n}] Error generating receipt preview:`,r.error),t.status(500).json({error:r.error});return console.log(`[${n}] Receipt preview generated successfully`),t.status(200).json({html:r.html,template:r.template})}catch(e){return console.error("Receipt preview API error:",e),t.status(500).json({error:"Internal server error"})}}let v=(0,n.l)(s,"default"),f=(0,n.l)(s,"config"),b=new i.PagesAPIRouteModule({definition:{kind:l.x.PAGES_API,page:"/api/admin/receipts/preview",pathname:"/api/admin/receipts/preview",bundlePath:"",filename:""},userland:s})},8456:(e,t,a)=>{a.d(t,{pR:()=>o});var s=a(2885);let i="https://ndlgbcsbidyhxbpqzgqp.supabase.co",l="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",n=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!i||!l)throw Error("Missing Supabase environment variables");(0,s.createClient)(i,l,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}});let o=(0,s.createClient)(i,n||l,{auth:{autoRefreshToken:!1,persistSession:!1}})}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[2805],()=>a(7922));module.exports=s})();