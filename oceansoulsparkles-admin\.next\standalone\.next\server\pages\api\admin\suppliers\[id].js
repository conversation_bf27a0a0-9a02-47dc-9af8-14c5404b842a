"use strict";(()=>{var e={};e.id=1010,e.ids=[1010],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},546:(e,r,s)=>{s.r(r),s.d(r,{config:()=>f,default:()=>m,routeModule:()=>h});var t={};s.r(t),s.d(t,{default:()=>l});var i=s(1802),a=s(7153),n=s(8781),o=s(7474),u=s(6482);async function l(e,r){let s=`supplier-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;try{let{id:t}=e.query;if(console.log(`[${s}] Individual supplier API request:`,{method:e.method,supplierId:t,userAgent:e.headers["user-agent"]}),!t||"string"!=typeof t)return r.status(400).json({error:"Invalid supplier ID",requestId:s});let i=await (0,o.Wg)(e);if(!i.valid)return console.log(`[${s}] Authentication failed:`,i.error),r.status(401).json({error:"Unauthorized",requestId:s});if(!["DEV","Admin"].includes(i.user.role))return console.log(`[${s}] Insufficient permissions:`,i.user.role),r.status(403).json({error:"Insufficient permissions",requestId:s});if("GET"===e.method)return await d(e,r,s,t);if("PUT"===e.method)return await p(e,r,s,t,i.user);if("DELETE"===e.method)return await c(e,r,s,t,i.user);return r.status(405).json({error:"Method not allowed",requestId:s})}catch(e){return console.error(`[${s}] Individual supplier API error:`,e),r.status(500).json({error:"Internal server error",message:e.message,requestId:s})}}async function d(e,r,s,t){try{let{data:e,error:i}=await u.supabaseAdmin.from("suppliers").select("*").eq("id",t).single();if(i){if("PGRST116"===i.code)return r.status(404).json({error:"Supplier not found",requestId:s});throw i}let{data:a,error:n}=await u.supabaseAdmin.from("inventory").select("id, name, sku, quantity_on_hand, min_stock_level").eq("supplier_id",t).eq("is_active",!0).order("name");n&&console.warn(`[${s}] Error fetching inventory items:`,n);let{data:o,error:l}=await u.supabaseAdmin.from("purchase_orders").select("id, po_number, status, order_date, total_amount").eq("supplier_id",t).order("order_date",{ascending:!1}).limit(10);return l&&console.warn(`[${s}] Error fetching purchase orders:`,l),console.log(`[${s}] Supplier fetched successfully:`,{id:e.id,name:e.name,inventoryItems:a?.length||0,purchaseOrders:o?.length||0}),r.status(200).json({supplier:e,inventoryItems:a||[],purchaseOrders:o||[],requestId:s})}catch(e){throw console.error(`[${s}] Error fetching supplier:`,e),e}}async function p(e,r,s,t,i){try{let{name:a,contactPerson:n,email:o,phone:l,address:d,website:p,paymentTerms:c,leadTimeDays:m,minimumOrderAmount:f,isActive:h,notes:g}=e.body;if(!a||0===a.trim().length)return r.status(400).json({error:"Validation failed",message:"Supplier name is required",requestId:s});let{data:w,error:I}=await u.supabaseAdmin.from("suppliers").select("id, name").eq("id",t).single();if(I){if("PGRST116"===I.code)return r.status(404).json({error:"Supplier not found",requestId:s});throw I}let{data:b}=await u.supabaseAdmin.from("suppliers").select("id").eq("name",a.trim()).neq("id",t).single();if(b)return r.status(409).json({error:"Supplier name already exists",message:"Another supplier with this name already exists",requestId:s});if(o&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o))return r.status(400).json({error:"Validation failed",message:"Invalid email format",requestId:s});let y={name:a.trim(),contact_person:n?.trim()||null,email:o?.trim()||null,phone:l?.trim()||null,address:d?.trim()||null,website:p?.trim()||null,payment_terms:c?.trim()||"Net 30",lead_time_days:m?parseInt(m):7,minimum_order_amount:f?parseFloat(f):0,is_active:void 0===h||!!h,notes:g?.trim()||null,updated_at:new Date().toISOString()},{data:_,error:S}=await u.supabaseAdmin.from("suppliers").update(y).eq("id",t).select().single();if(S)throw console.error(`[${s}] Database error updating supplier:`,S),S;return console.log(`[${s}] Supplier updated successfully:`,{id:_.id,name:_.name,updatedBy:i.id}),r.status(200).json({supplier:_,message:"Supplier updated successfully",requestId:s})}catch(e){throw console.error(`[${s}] Error updating supplier:`,e),e}}async function c(e,r,s,t,i){try{let{data:e,error:a}=await u.supabaseAdmin.from("suppliers").select("id, name").eq("id",t).single();if(a){if("PGRST116"===a.code)return r.status(404).json({error:"Supplier not found",requestId:s});throw a}let{data:n,error:o}=await u.supabaseAdmin.from("purchase_orders").select("id").eq("supplier_id",t).in("status",["draft","sent","confirmed"]).limit(1);if(o&&console.warn(`[${s}] Error checking purchase orders:`,o),n&&n.length>0)return r.status(409).json({error:"Cannot delete supplier",message:"Supplier has active purchase orders. Please complete or cancel them first.",requestId:s});let{data:l,error:d}=await u.supabaseAdmin.from("suppliers").update({is_active:!1,updated_at:new Date().toISOString()}).eq("id",t).select().single();if(d)throw console.error(`[${s}] Database error deleting supplier:`,d),d;return console.log(`[${s}] Supplier deleted successfully:`,{id:l.id,name:l.name,deletedBy:i.id}),r.status(200).json({message:"Supplier deleted successfully",requestId:s})}catch(e){throw console.error(`[${s}] Error deleting supplier:`,e),e}}let m=(0,n.l)(t,"default"),f=(0,n.l)(t,"config"),h=new i.PagesAPIRouteModule({definition:{kind:a.x.PAGES_API,page:"/api/admin/suppliers/[id]",pathname:"/api/admin/suppliers/[id]",bundlePath:"",filename:""},userland:t})},6482:(e,r,s)=>{s.r(r),s.d(r,{supabaseAdmin:()=>n});var t=s(2885);let i="https://ndlgbcsbidyhxbpqzgqp.supabase.co",a=process.env.SUPABASE_SERVICE_ROLE_KEY;i&&a||console.warn("Missing Supabase environment variables for admin client");let n=(0,t.createClient)(i,a||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",{auth:{autoRefreshToken:!1,persistSession:!1}})}};var r=require("../../../../webpack-api-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[2805],()=>s(546));module.exports=t})();