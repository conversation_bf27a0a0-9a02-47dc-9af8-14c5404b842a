["@aws-sdk/client-s3", "@aws-sdk/s3-presigned-post", "@blockfrost/blockfrost-js", "@highlight-run/node", "@jpg-store/lucid-cardano", "@libsql/client", "@mikro-orm/core", "@mikro-orm/knex", "@prisma/client", "@react-pdf/renderer", "@sentry/profiling-node", "@swc/core", "argon2", "autoprefixer", "aws-crt", "bcrypt", "better-sqlite3", "canvas", "cpu-features", "cypress", "eslint", "express", "firebase-admin", "jest", "jsdom", "libsql", "mdx-bundler", "mongodb", "mongoose", "next-mdx-remote", "next-seo", "node-pty", "node-web-audio-api", "pg", "playwright", "postcss", "prettier", "prisma", "puppeteer", "puppeteer-core", "<PERSON><PERSON><PERSON>", "sharp", "shiki", "sqlite3", "tailwindcss", "ts-node", "typescript", "vscode-oniguruma", "webpack", "websocket", "zeromq"]