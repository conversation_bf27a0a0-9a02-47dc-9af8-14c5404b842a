"use strict";(()=>{var e={};e.id=3397,e.ids=[3397],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},368:(e,t,r)=>{r.r(t),r.d(t,{config:()=>d,default:()=>c,routeModule:()=>l});var o={};r.r(o),r.d(o,{default:()=>u});var i=r(1802),n=r(7153),a=r(8781),s=r(8456),m=r(7474);async function u(e,t){if("POST"!==e.method)return t.status(405).json({error:"Method not allowed"});let r=Math.random().toString(36).substring(2,8);console.log(`[${r}] POS booking creation API called`);try{let{user:o,error:i}=await (0,m.ZQ)(e);if(i||!o)return t.status(401).json({error:"Authentication required",message:i?.message||"Authentication failed",requestId:r});console.log(`[${r}] Authentication successful. User: ${o?.email}`);let{service:n,artist:a,tier:u,timeSlot:c,customer:d,payment:l}=e.body;if(!n?.id||!a?.id||!u?.id||!c?.time||!d||!l)return t.status(400).json({error:"Missing required data",message:"Service, artist, tier, timeSlot, customer, and payment information are required",requestId:r});if("string"!=typeof n.id||""===n.id.trim()||"string"!=typeof a.id||""===a.id.trim()||"string"!=typeof u.id||""===u.id.trim())return t.status(400).json({error:"Invalid input",message:"Service ID, Artist ID, and Tier ID must be non-empty strings.",requestId:r});if("number"!=typeof u.duration||u.duration<=0)return t.status(400).json({error:"Invalid input",message:"Tier duration must be a positive number.",requestId:r});if("number"!=typeof u.price||u.price<0)return t.status(400).json({error:"Invalid input",message:"Tier price must be a non-negative number.",requestId:r});if(!c.time||"string"!=typeof c.time||isNaN(new Date(c.time).getTime()))return t.status(400).json({error:"Invalid input",message:"Invalid timeSlot.time format. Expected valid ISO string.",requestId:r});if(d.email&&("string"!=typeof d.email||!d.email.includes("@")||!d.email.includes(".")))return t.status(400).json({error:"Invalid input",message:"Invalid customer email format.",requestId:r});if(!d.isAnonymous&&(!d.name||"string"!=typeof d.name||""===d.name.trim()))return t.status(400).json({error:"Invalid input",message:"Customer name is required for non-anonymous customers.",requestId:r});if("number"!=typeof l.amount||l.amount<0)return t.status(400).json({error:"Invalid input",message:"Payment amount must be a non-negative number.",requestId:r});if("string"!=typeof l.method||""===l.method.trim())return t.status(400).json({error:"Invalid input",message:"Payment method must be a non-empty string.",requestId:r});console.log(`[${r}] Creating POS booking for service: ${n.name}, artist: ${a.name}`);let p=null,g=null,f=null;try{let e=`pos_${Date.now()}_${Math.random().toString(36).substring(2,8)}`;if(d.isAnonymous)try{let{data:e,error:t}=await s.pR.from("customers").insert([{name:d.name||`Walk-in Customer #${Date.now()}`,email:null,phone:d.phone||null,notes:"POS anonymous customer",created_via:"pos"}]).select().single();if(t)throw console.error(`[${r}] Anonymous customer creation failed:`,t),Error(`Anonymous customer creation failed: ${t.message}`);p=e.id,console.log(`[${r}] Created anonymous customer: ${p}`)}catch(e){throw console.error(`[${r}] Anonymous customer creation failed:`,e),Error(`Anonymous customer creation failed: ${e.message}`)}else try{let{data:e,error:t}=await s.pR.from("customers").insert([{name:d.name,email:d.email||null,phone:d.phone||null,notes:d.notes||"POS customer",created_via:"pos"}]).select().single();if(t)throw console.error(`[${r}] Named customer creation failed:`,t),Error(`Named customer creation failed: ${t.message}`);p=e.id,console.log(`[${r}] Created new customer: ${p} (${d.email||"no email"})`)}catch(e){throw console.error(`[${r}] Named customer creation failed:`,e),Error(`Customer creation failed: ${e.message}`)}let i=new Date(c.time),m=new Date(i.getTime()+6e4*u.duration);try{let{data:t,error:o}=await s.pR.from("bookings").insert([{customer_id:p,service_id:n.id,artist_id:a.id,assigned_artist_id:a.id,start_time:i.toISOString(),end_time:m.toISOString(),status:"confirmed",location:"POS Terminal",notes:`POS booking - ${u.name} tier - ${l.method} payment - Artist: ${a.name}`,booking_source:"pos",pos_session_id:e,tier_name:u.name,tier_price:u.price,total_amount:l.amount}]).select().single();if(o)throw console.error(`[${r}] Error creating booking:`,o),Error(`Failed to create booking: ${o.message}`);g=t.id,console.log(`[${r}] Created POS booking: ${g} for session: ${e}`)}catch(e){throw console.error(`[${r}] Booking creation failed:`,e),Error(`Booking creation failed: ${e.message}`)}try{let{data:t,error:i}=await s.pR.from("payments").insert([{booking_id:g,amount:l.amount,currency:l.currency||"AUD",method:l.method,status:"completed",transaction_id:l.details?.transactionId||null,cash_received:l.details?.cashReceived||null,change_amount:l.details?.changeAmount||null,tip_amount:l.tip_amount||0,tip_method:l.tip_method||"none",tip_percentage:l.tip_percentage||0,processing_fee:l.details?.processingFee||0,payment_time:new Date().toISOString(),receipt_data:l.details?JSON.stringify(l.details):null}]).select().single();if(i)throw console.error(`[${r}] Error creating payment:`,i),Error(`Failed to create payment record: ${i.message}`);if(f=t.id,console.log(`[${r}] Created POS payment: ${f} for session: ${e}`),l.tip_amount&&l.tip_amount>0)try{let{data:e,error:t}=await s.pR.from("tips").insert([{payment_id:f,booking_id:g,artist_id:a.id,amount:l.tip_amount,currency:l.currency||"AUD",tip_method:l.tip_method,percentage:l.tip_percentage,distribution_status:"pending",notes:`POS tip for ${n.name} - ${a.name}`,created_by:o.id}]).select().single();t?console.error(`[${r}] Error creating tip record:`,t):console.log(`[${r}] Tip record created:`,e)}catch(e){console.error(`[${r}] Error creating tip record:`,e)}}catch(e){throw console.error(`[${r}] Payment creation failed:`,e),Error(`Payment creation failed: ${e.message}`)}return t.status(201).json({success:!0,booking:{id:g,customer_id:p,service_id:n.id,start_time:i.toISOString(),end_time:m.toISOString(),status:"confirmed"},payment:{id:f,amount:l.amount,currency:l.currency||"AUD",method:l.method,status:"completed"},customer:{id:p,name:d.name,email:d.email||null},receipt:{service:n.name,tier:u.name,duration:u.duration,amount:l.amount,currency:l.currency||"AUD",payment_method:l.method,booking_time:i.toISOString(),booking_id:g}})}catch(e){throw console.error(`[${r}] Transaction error:`,e),f&&await s.pR.from("payments").delete().eq("id",f),g&&await s.pR.from("bookings").delete().eq("id",g),p&&d.isAnonymous&&await s.pR.from("customers").delete().eq("id",p),e}}catch(i){console.error(`[${r}] POS Booking Creation Error:`,i);let e=500,o="Failed to create booking";return i.message&&i.message.includes("validation")?(e=400,o=i.message):i.message&&i.message.includes("not found")?(e=404,o=i.message):i.message&&(o=i.message),t.status(e).json({error:"Booking creation failed",message:o,requestId:r})}}let c=(0,a.l)(o,"default"),d=(0,a.l)(o,"config"),l=new i.PagesAPIRouteModule({definition:{kind:n.x.PAGES_API,page:"/api/admin/pos/create-booking",pathname:"/api/admin/pos/create-booking",bundlePath:"",filename:""},userland:o})},8456:(e,t,r)=>{r.d(t,{pR:()=>s});var o=r(2885);let i="https://ndlgbcsbidyhxbpqzgqp.supabase.co",n="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",a=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!i||!n)throw Error("Missing Supabase environment variables");(0,o.createClient)(i,n,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}});let s=(0,o.createClient)(i,a||n,{auth:{autoRefreshToken:!1,persistSession:!1}})}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[2805],()=>r(368));module.exports=o})();