"use strict";(()=>{var e={};e.id=470,e.ids=[470],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6555:e=>{e.exports=import("uuid")},8781:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,s){return s in t?t[s]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,s)):"function"==typeof t&&"default"===s?t:void 0}}})},2881:(e,t,s)=>{s.a(e,async(e,i)=>{try{s.r(t),s.d(t,{config:()=>u,default:()=>d,routeModule:()=>c});var o=s(1802),a=s(7153),r=s(8781),n=s(930),m=e([n]);n=(m.then?(await m)():m)[0];let d=(0,r.l)(n,"default"),u=(0,r.l)(n,"config"),c=new o.PagesAPIRouteModule({definition:{kind:a.x.PAGES_API,page:"/api/admin/artists/commissions",pathname:"/api/admin/artists/commissions",bundlePath:"",filename:""},userland:n});i()}catch(e){i(e)}})},930:(e,t,s)=>{s.a(e,async(e,i)=>{try{s.r(t),s.d(t,{default:()=>n});var o=s(2885),a=s(6555),r=e([a]);a=(r.then?(await r)():r)[0];let m=process.env.SUPABASE_SERVICE_ROLE_KEY,d=(0,o.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",m);async function n(e,t){let s=(0,a.v4)();try{let i=e.headers.authorization;if(!i||!i.startsWith("Bearer "))return t.status(401).json({error:"Unauthorized",message:"Valid authentication token required",requestId:s});if("GET"===e.method){let{artist_id:i,status:o,start_date:a,end_date:r,payment_method:n,limit:m=50,offset:u=0}=e.query,c=d.from("commission_transactions").select(`
          id,
          artist_id,
          booking_id,
          payment_id,
          service_amount,
          commission_rate,
          commission_amount,
          tip_amount,
          total_earnings,
          status,
          payment_method,
          paid_at,
          paid_by,
          notes,
          created_at,
          updated_at,
          artist_profiles!inner(
            id,
            name,
            email,
            commission_rate as default_commission_rate
          ),
          bookings!inner(
            id,
            start_time,
            end_time,
            status as booking_status,
            customers(
              id,
              first_name,
              last_name,
              email
            ),
            services(
              id,
              name,
              category
            )
          ),
          payments(
            id,
            amount,
            method,
            status as payment_status,
            payment_time
          ),
          payer:admin_users!paid_by(
            id,
            first_name,
            last_name,
            email
          )
        `).order("created_at",{ascending:!1});i&&(c=c.eq("artist_id",i)),o&&(c=c.eq("status",o)),n&&(c=c.eq("payment_method",n)),a&&(c=c.gte("created_at",a)),r&&(c=c.lte("created_at",r)),c=c.range(parseInt(u),parseInt(u)+parseInt(m)-1);let{data:l,error:_}=await c;if(_)return console.error("Commissions fetch error:",_),t.status(500).json({error:"Database error",message:"Failed to fetch commission transactions",requestId:s});let{count:p}=await d.from("commission_transactions").select("*",{count:"exact",head:!0}),f=l||[],g={total:f.length,totalEarnings:f.reduce((e,t)=>e+(t.total_earnings||0),0),totalCommissions:f.reduce((e,t)=>e+(t.commission_amount||0),0),totalTips:f.reduce((e,t)=>e+(t.tip_amount||0),0),pending:f.filter(e=>"pending"===e.status).length,calculated:f.filter(e=>"calculated"===e.status).length,paid:f.filter(e=>"paid"===e.status).length,disputed:f.filter(e=>"disputed"===e.status).length,averageCommissionRate:f.length>0?f.reduce((e,t)=>e+(t.commission_rate||0),0)/f.length:0};return t.status(200).json({commissions:f,stats:g,pagination:{total:p||0,limit:parseInt(m),offset:parseInt(u),hasMore:parseInt(u)+parseInt(m)<(p||0)},requestId:s})}if("POST"===e.method){let i=e.body;if(!i.artist_id||!i.booking_id||void 0===i.service_amount||void 0===i.commission_rate)return t.status(400).json({error:"Validation error",message:"Missing required fields: artist_id, booking_id, service_amount, commission_rate",requestId:s});let{data:o,error:r}=await d.from("artist_profiles").select("id, name, commission_rate").eq("id",i.artist_id).single();if(r||!o)return t.status(404).json({error:"Artist not found",message:"The specified artist does not exist",requestId:s});let{data:n,error:m}=await d.from("bookings").select("id, total_amount, status, artist_id").eq("id",i.booking_id).single();if(m||!n)return t.status(404).json({error:"Booking not found",message:"The specified booking does not exist",requestId:s});if(n.artist_id!==i.artist_id)return t.status(400).json({error:"Invalid booking",message:"The booking does not belong to the specified artist",requestId:s});let{data:u,error:c}=await d.from("commission_transactions").select("id").eq("booking_id",i.booking_id).single();if(c&&"PGRST116"!==c.code)console.error("Existing commission check error:",c);else if(u)return t.status(409).json({error:"Commission exists",message:"A commission transaction already exists for this booking",requestId:s});let l=i.service_amount*i.commission_rate/100,_=i.tip_amount||0,p={id:(0,a.v4)(),artist_id:i.artist_id,booking_id:i.booking_id,payment_id:i.payment_id||null,service_amount:i.service_amount,commission_rate:i.commission_rate,commission_amount:l,tip_amount:_,total_earnings:l+_,status:i.status||"calculated",payment_method:i.payment_method||null,notes:i.notes||null,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:f,error:g}=await d.from("commission_transactions").insert([p]).select(`
          *,
          artist_profiles!inner(
            id,
            name,
            email
          ),
          bookings!inner(
            id,
            start_time,
            customers(first_name, last_name),
            services(name)
          )
        `).single();if(g)return console.error("Commission creation error:",g),t.status(500).json({error:"Database error",message:"Failed to create commission transaction",requestId:s});return t.status(201).json({commission:f,message:"Commission transaction created successfully",requestId:s})}if("PUT"===e.method){let{commission_id:i}=e.query,o=e.body;if(!i)return t.status(400).json({error:"Validation error",message:"Commission ID is required",requestId:s});let{data:a,error:r}=await d.from("commission_transactions").select("*").eq("id",i).single();if(r||!a)return t.status(404).json({error:"Commission not found",message:"The specified commission transaction does not exist",requestId:s});if("paid"!==o.status||o.paid_at||(o.paid_at=new Date().toISOString()),void 0!==o.service_amount||void 0!==o.commission_rate||void 0!==o.tip_amount){let e=o.service_amount??a.service_amount,t=o.commission_rate??a.commission_rate,s=o.tip_amount??a.tip_amount;o.commission_amount=e*t/100,o.total_earnings=o.commission_amount+s}let n={...o,updated_at:new Date().toISOString()},{data:m,error:u}=await d.from("commission_transactions").update(n).eq("id",i).select(`
          *,
          artist_profiles!inner(
            id,
            name,
            email
          ),
          bookings!inner(
            id,
            start_time,
            customers(first_name, last_name),
            services(name)
          ),
          payer:admin_users!paid_by(
            id,
            first_name,
            last_name,
            email
          )
        `).single();if(u)return console.error("Commission update error:",u),t.status(500).json({error:"Database error",message:"Failed to update commission transaction",requestId:s});return t.status(200).json({commission:m,message:"Commission transaction updated successfully",requestId:s})}if("DELETE"===e.method){let{commission_id:i}=e.query;if(!i)return t.status(400).json({error:"Validation error",message:"Commission ID is required",requestId:s});let{data:o,error:a}=await d.from("commission_transactions").select("id, status, total_earnings").eq("id",i).single();if(a||!o)return t.status(404).json({error:"Commission not found",message:"The specified commission transaction does not exist",requestId:s});if(["paid","disputed"].includes(o.status))return t.status(400).json({error:"Invalid operation",message:"Cannot delete paid or disputed commission transactions",requestId:s});let{error:r}=await d.from("commission_transactions").delete().eq("id",i);if(r)return console.error("Commission deletion error:",r),t.status(500).json({error:"Database error",message:"Failed to delete commission transaction",requestId:s});return t.status(200).json({message:"Commission transaction deleted successfully",deletedCommission:o,requestId:s})}return t.status(405).json({error:"Method not allowed",message:`HTTP method ${e.method} is not supported`,requestId:s})}catch(e){return console.error("Commission API error:",e),t.status(500).json({error:"Internal server error",message:"An unexpected error occurred",requestId:s})}}i()}catch(e){i(e)}})},7153:(e,t)=>{var s;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return s}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(s||(s={}))},1802:(e,t,s)=>{e.exports=s(1287)}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var s=t(t.s=2881);module.exports=s})();