"use strict";(()=>{var e={};e.id=4370,e.ids=[4370],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5828:e=>{e.exports=require("uuid")},8781:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},5681:(e,t,r)=>{r.r(t),r.d(t,{config:()=>c,default:()=>_,routeModule:()=>m});var s={};r.r(s),r.d(s,{default:()=>f});var a=r(1802),n=r(7153),d=r(8781),i=r(2885),u=r(5828);let o=process.env.SUPABASE_SERVICE_ROLE_KEY,l=(0,i.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",o);async function f(e,t){let r=(0,u.v4)();try{let s=e.headers.authorization;if(!s||!s.startsWith("Bearer "))return t.status(401).json({error:"Unauthorized",message:"Valid authentication token required",requestId:r});if("GET"===e.method){let{staff_id:s,request_type:a,status:n,start_date:d,end_date:i,limit:u=50,offset:o=0}=e.query,f=l.from("staff_schedule_requests").select(`
          id,
          staff_id,
          request_type,
          start_date,
          end_date,
          start_time,
          end_time,
          reason,
          replacement_staff_id,
          status,
          priority,
          approved_by,
          approved_at,
          denial_reason,
          created_at,
          updated_at,
          admin_users!staff_id(
            id,
            first_name,
            last_name,
            email,
            role
          ),
          replacement_staff:admin_users!replacement_staff_id(
            id,
            first_name,
            last_name,
            email
          ),
          approver:admin_users!approved_by(
            id,
            first_name,
            last_name,
            email
          )
        `).order("created_at",{ascending:!1});s&&(f=f.eq("staff_id",s)),a&&(f=f.eq("request_type",a)),n&&(f=f.eq("status",n)),d&&(f=f.gte("start_date",d)),i&&(f=f.lte("end_date",i)),f=f.range(parseInt(o),parseInt(o)+parseInt(u)-1);let{data:_,error:c}=await f;if(c)return console.error("Schedule requests fetch error:",c),t.status(500).json({error:"Database error",message:"Failed to fetch schedule requests",requestId:r});let{count:m}=await l.from("staff_schedule_requests").select("*",{count:"exact",head:!0}),p={total:_?.length||0,pending:_?.filter(e=>"pending"===e.status).length||0,approved:_?.filter(e=>"approved"===e.status).length||0,denied:_?.filter(e=>"denied"===e.status).length||0,byType:{time_off:_?.filter(e=>"time_off"===e.request_type).length||0,schedule_change:_?.filter(e=>"schedule_change"===e.request_type).length||0,shift_swap:_?.filter(e=>"shift_swap"===e.request_type).length||0}};return t.status(200).json({scheduleRequests:_||[],stats:p,pagination:{total:m||0,limit:parseInt(u),offset:parseInt(o),hasMore:parseInt(o)+parseInt(u)<(m||0)},requestId:r})}if("POST"===e.method){let s=e.body;if(!s.staff_id||!s.request_type||!s.start_date||!s.end_date)return t.status(400).json({error:"Validation error",message:"Missing required fields: staff_id, request_type, start_date, end_date",requestId:r});let{data:a,error:n}=await l.from("admin_users").select("id, first_name, last_name, email, role").eq("id",s.staff_id).single();if(n||!a)return t.status(404).json({error:"Staff member not found",message:"The specified staff member does not exist",requestId:r});if(s.replacement_staff_id){let{data:e,error:a}=await l.from("admin_users").select("id, first_name, last_name").eq("id",s.replacement_staff_id).single();if(a||!e)return t.status(404).json({error:"Replacement staff not found",message:"The specified replacement staff member does not exist",requestId:r})}let{data:d,error:i}=await l.from("staff_schedule_requests").select("id, start_date, end_date, status").eq("staff_id",s.staff_id).in("status",["pending","approved"]).or(`and(start_date.lte.${s.end_date},end_date.gte.${s.start_date})`);if(i)console.error("Conflict check error:",i);else if(d&&d.length>0)return t.status(409).json({error:"Schedule conflict",message:"There are conflicting schedule requests for the selected dates",conflictingRequests:d,requestId:r});let o={id:(0,u.v4)(),staff_id:s.staff_id,request_type:s.request_type,start_date:s.start_date,end_date:s.end_date,start_time:s.start_time||null,end_time:s.end_time||null,reason:s.reason||null,replacement_staff_id:s.replacement_staff_id||null,status:"pending",priority:s.priority||"normal",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:f,error:_}=await l.from("staff_schedule_requests").insert([o]).select(`
          *,
          admin_users!staff_id(
            id,
            first_name,
            last_name,
            email,
            role
          ),
          replacement_staff:admin_users!replacement_staff_id(
            id,
            first_name,
            last_name,
            email
          )
        `).single();if(_)return console.error("Schedule request creation error:",_),t.status(500).json({error:"Database error",message:"Failed to create schedule request",requestId:r});return t.status(201).json({scheduleRequest:f,message:"Schedule request created successfully",requestId:r})}if("PUT"===e.method){let{request_id:s}=e.query,a=e.body;if(!s)return t.status(400).json({error:"Validation error",message:"Schedule request ID is required",requestId:r});let{data:n,error:d}=await l.from("staff_schedule_requests").select("*").eq("id",s).single();if(d||!n)return t.status(404).json({error:"Schedule request not found",message:"The specified schedule request does not exist",requestId:r});a.status&&["approved","denied"].includes(a.status)&&(a.approved_at=new Date().toISOString());let i={...a,updated_at:new Date().toISOString()},{data:u,error:o}=await l.from("staff_schedule_requests").update(i).eq("id",s).select(`
          *,
          admin_users!staff_id(
            id,
            first_name,
            last_name,
            email,
            role
          ),
          replacement_staff:admin_users!replacement_staff_id(
            id,
            first_name,
            last_name,
            email
          ),
          approver:admin_users!approved_by(
            id,
            first_name,
            last_name,
            email
          )
        `).single();if(o)return console.error("Schedule request update error:",o),t.status(500).json({error:"Database error",message:"Failed to update schedule request",requestId:r});return t.status(200).json({scheduleRequest:u,message:"Schedule request updated successfully",requestId:r})}if("DELETE"===e.method){let{request_id:s}=e.query;if(!s)return t.status(400).json({error:"Validation error",message:"Schedule request ID is required",requestId:r});let{data:a,error:n}=await l.from("staff_schedule_requests").select("id, staff_id, status").eq("id",s).single();if(n||!a)return t.status(404).json({error:"Schedule request not found",message:"The specified schedule request does not exist",requestId:r});if("pending"!==a.status)return t.status(400).json({error:"Invalid operation",message:"Only pending schedule requests can be deleted",requestId:r});let{error:d}=await l.from("staff_schedule_requests").delete().eq("id",s);if(d)return console.error("Schedule request deletion error:",d),t.status(500).json({error:"Database error",message:"Failed to delete schedule request",requestId:r});return t.status(200).json({message:"Schedule request deleted successfully",deletedRequest:a,requestId:r})}return t.status(405).json({error:"Method not allowed",message:`HTTP method ${e.method} is not supported`,requestId:r})}catch(e){return console.error("Schedule API error:",e),t.status(500).json({error:"Internal server error",message:"An unexpected error occurred",requestId:r})}}let _=(0,d.l)(s,"default"),c=(0,d.l)(s,"config"),m=new a.PagesAPIRouteModule({definition:{kind:n.x.PAGES_API,page:"/api/admin/staff/schedule",pathname:"/api/admin/staff/schedule",bundlePath:"",filename:""},userland:s})},7153:(e,t)=>{var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},1802:(e,t,r)=>{e.exports=r(1287)}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=5681);module.exports=r})();