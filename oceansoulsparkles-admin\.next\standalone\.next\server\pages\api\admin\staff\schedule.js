"use strict";(()=>{var e={};e.id=4370,e.ids=[4370],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6555:e=>{e.exports=import("uuid")},8781:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,s){return s in t?t[s]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,s)):"function"==typeof t&&"default"===s?t:void 0}}})},5:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{config:()=>l,default:()=>o,routeModule:()=>f});var a=s(1802),n=s(7153),d=s(8781),i=s(7580),u=e([i]);i=(u.then?(await u)():u)[0];let o=(0,d.l)(i,"default"),l=(0,d.l)(i,"config"),f=new a.PagesAPIRouteModule({definition:{kind:n.x.PAGES_API,page:"/api/admin/staff/schedule",pathname:"/api/admin/staff/schedule",bundlePath:"",filename:""},userland:i});r()}catch(e){r(e)}})},7580:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{default:()=>i});var a=s(2885),n=s(6555),d=e([n]);n=(d.then?(await d)():d)[0];let u=process.env.SUPABASE_SERVICE_ROLE_KEY,o=(0,a.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",u);async function i(e,t){let s=(0,n.v4)();try{let r=e.headers.authorization;if(!r||!r.startsWith("Bearer "))return t.status(401).json({error:"Unauthorized",message:"Valid authentication token required",requestId:s});if("GET"===e.method){let{staff_id:r,request_type:a,status:n,start_date:d,end_date:i,limit:u=50,offset:l=0}=e.query,f=o.from("staff_schedule_requests").select(`
          id,
          staff_id,
          request_type,
          start_date,
          end_date,
          start_time,
          end_time,
          reason,
          replacement_staff_id,
          status,
          priority,
          approved_by,
          approved_at,
          denial_reason,
          created_at,
          updated_at,
          admin_users!staff_id(
            id,
            first_name,
            last_name,
            email,
            role
          ),
          replacement_staff:admin_users!replacement_staff_id(
            id,
            first_name,
            last_name,
            email
          ),
          approver:admin_users!approved_by(
            id,
            first_name,
            last_name,
            email
          )
        `).order("created_at",{ascending:!1});r&&(f=f.eq("staff_id",r)),a&&(f=f.eq("request_type",a)),n&&(f=f.eq("status",n)),d&&(f=f.gte("start_date",d)),i&&(f=f.lte("end_date",i)),f=f.range(parseInt(l),parseInt(l)+parseInt(u)-1);let{data:_,error:c}=await f;if(c)return console.error("Schedule requests fetch error:",c),t.status(500).json({error:"Database error",message:"Failed to fetch schedule requests",requestId:s});let{count:m}=await o.from("staff_schedule_requests").select("*",{count:"exact",head:!0}),p={total:_?.length||0,pending:_?.filter(e=>"pending"===e.status).length||0,approved:_?.filter(e=>"approved"===e.status).length||0,denied:_?.filter(e=>"denied"===e.status).length||0,byType:{time_off:_?.filter(e=>"time_off"===e.request_type).length||0,schedule_change:_?.filter(e=>"schedule_change"===e.request_type).length||0,shift_swap:_?.filter(e=>"shift_swap"===e.request_type).length||0}};return t.status(200).json({scheduleRequests:_||[],stats:p,pagination:{total:m||0,limit:parseInt(u),offset:parseInt(l),hasMore:parseInt(l)+parseInt(u)<(m||0)},requestId:s})}if("POST"===e.method){let r=e.body;if(!r.staff_id||!r.request_type||!r.start_date||!r.end_date)return t.status(400).json({error:"Validation error",message:"Missing required fields: staff_id, request_type, start_date, end_date",requestId:s});let{data:a,error:d}=await o.from("admin_users").select("id, first_name, last_name, email, role").eq("id",r.staff_id).single();if(d||!a)return t.status(404).json({error:"Staff member not found",message:"The specified staff member does not exist",requestId:s});if(r.replacement_staff_id){let{data:e,error:a}=await o.from("admin_users").select("id, first_name, last_name").eq("id",r.replacement_staff_id).single();if(a||!e)return t.status(404).json({error:"Replacement staff not found",message:"The specified replacement staff member does not exist",requestId:s})}let{data:i,error:u}=await o.from("staff_schedule_requests").select("id, start_date, end_date, status").eq("staff_id",r.staff_id).in("status",["pending","approved"]).or(`and(start_date.lte.${r.end_date},end_date.gte.${r.start_date})`);if(u)console.error("Conflict check error:",u);else if(i&&i.length>0)return t.status(409).json({error:"Schedule conflict",message:"There are conflicting schedule requests for the selected dates",conflictingRequests:i,requestId:s});let l={id:(0,n.v4)(),staff_id:r.staff_id,request_type:r.request_type,start_date:r.start_date,end_date:r.end_date,start_time:r.start_time||null,end_time:r.end_time||null,reason:r.reason||null,replacement_staff_id:r.replacement_staff_id||null,status:"pending",priority:r.priority||"normal",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:f,error:_}=await o.from("staff_schedule_requests").insert([l]).select(`
          *,
          admin_users!staff_id(
            id,
            first_name,
            last_name,
            email,
            role
          ),
          replacement_staff:admin_users!replacement_staff_id(
            id,
            first_name,
            last_name,
            email
          )
        `).single();if(_)return console.error("Schedule request creation error:",_),t.status(500).json({error:"Database error",message:"Failed to create schedule request",requestId:s});return t.status(201).json({scheduleRequest:f,message:"Schedule request created successfully",requestId:s})}if("PUT"===e.method){let{request_id:r}=e.query,a=e.body;if(!r)return t.status(400).json({error:"Validation error",message:"Schedule request ID is required",requestId:s});let{data:n,error:d}=await o.from("staff_schedule_requests").select("*").eq("id",r).single();if(d||!n)return t.status(404).json({error:"Schedule request not found",message:"The specified schedule request does not exist",requestId:s});a.status&&["approved","denied"].includes(a.status)&&(a.approved_at=new Date().toISOString());let i={...a,updated_at:new Date().toISOString()},{data:u,error:l}=await o.from("staff_schedule_requests").update(i).eq("id",r).select(`
          *,
          admin_users!staff_id(
            id,
            first_name,
            last_name,
            email,
            role
          ),
          replacement_staff:admin_users!replacement_staff_id(
            id,
            first_name,
            last_name,
            email
          ),
          approver:admin_users!approved_by(
            id,
            first_name,
            last_name,
            email
          )
        `).single();if(l)return console.error("Schedule request update error:",l),t.status(500).json({error:"Database error",message:"Failed to update schedule request",requestId:s});return t.status(200).json({scheduleRequest:u,message:"Schedule request updated successfully",requestId:s})}if("DELETE"===e.method){let{request_id:r}=e.query;if(!r)return t.status(400).json({error:"Validation error",message:"Schedule request ID is required",requestId:s});let{data:a,error:n}=await o.from("staff_schedule_requests").select("id, staff_id, status").eq("id",r).single();if(n||!a)return t.status(404).json({error:"Schedule request not found",message:"The specified schedule request does not exist",requestId:s});if("pending"!==a.status)return t.status(400).json({error:"Invalid operation",message:"Only pending schedule requests can be deleted",requestId:s});let{error:d}=await o.from("staff_schedule_requests").delete().eq("id",r);if(d)return console.error("Schedule request deletion error:",d),t.status(500).json({error:"Database error",message:"Failed to delete schedule request",requestId:s});return t.status(200).json({message:"Schedule request deleted successfully",deletedRequest:a,requestId:s})}return t.status(405).json({error:"Method not allowed",message:`HTTP method ${e.method} is not supported`,requestId:s})}catch(e){return console.error("Schedule API error:",e),t.status(500).json({error:"Internal server error",message:"An unexpected error occurred",requestId:s})}}r()}catch(e){r(e)}})},7153:(e,t)=>{var s;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return s}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(s||(s={}))},1802:(e,t,s)=>{e.exports=s(1287)}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var s=t(t.s=5);module.exports=s})();