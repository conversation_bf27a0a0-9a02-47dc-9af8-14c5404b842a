"use strict";(()=>{var e={};e.id=7052,e.ids=[7052],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5828:e=>{e.exports=require("uuid")},8781:(e,r)=>{Object.defineProperty(r,"l",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},155:(e,r,t)=>{t.r(r),t.d(r,{config:()=>m,default:()=>c,routeModule:()=>p});var s={};t.r(s),t.d(s,{default:()=>_});var i=t(1802),a=t(7153),o=t(8781),d=t(2885),n=t(5828);let l=process.env.SUPABASE_SERVICE_ROLE_KEY,u=(0,d.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",l);async function _(e,r){let t=(0,n.v4)();try{let s=e.headers.authorization;if(!s||!s.startsWith("Bearer "))return r.status(401).json({error:"Unauthorized",message:"Valid authentication token required",requestId:t});if("GET"===e.method){let{artist_id:s,override_type:i,status:a,start_date:o,end_date:d,limit:n=50,offset:l=0}=e.query,_=u.from("artist_availability").select(`
          id,
          artist_id,
          day_of_week,
          start_time,
          end_time,
          break_start_time,
          break_end_time,
          is_available,
          created_at,
          artist_profiles!inner(
            id,
            name,
            email,
            is_active
          )
        `).order("day_of_week",{ascending:!0}),c=u.from("artist_schedule_overrides").select(`
          id,
          artist_id,
          override_date,
          override_type,
          start_time,
          end_time,
          reason,
          notes,
          status,
          approved_by,
          approved_at,
          created_by,
          created_at,
          updated_at,
          artist_profiles!inner(
            id,
            name,
            email
          ),
          approver:admin_users!approved_by(
            id,
            first_name,
            last_name,
            email
          ),
          creator:admin_users!created_by(
            id,
            first_name,
            last_name,
            email
          )
        `).order("override_date",{ascending:!1});s&&(_=_.eq("artist_id",s),c=c.eq("artist_id",s)),i&&(c=c.eq("override_type",i)),a&&(c=c.eq("status",a)),o&&(c=c.gte("override_date",o)),d&&(c=c.lte("override_date",d)),c=c.range(parseInt(l),parseInt(l)+parseInt(n)-1);let[{data:m,error:p},{data:f,error:v}]=await Promise.all([_,c]);if(p)return console.error("Availability fetch error:",p),r.status(500).json({error:"Database error",message:"Failed to fetch artist availability",requestId:t});if(v)return console.error("Overrides fetch error:",v),r.status(500).json({error:"Database error",message:"Failed to fetch schedule overrides",requestId:t});let{count:g}=await u.from("artist_schedule_overrides").select("*",{count:"exact",head:!0}),{data:h}=await u.from("bookings").select(`
          id,
          start_time,
          end_time,
          status,
          services(name),
          customers(first_name, last_name)
        `).gte("start_time",new Date().toISOString()).in("status",["pending","confirmed"]).order("start_time",{ascending:!0}).limit(10),b={totalOverrides:f?.length||0,pendingOverrides:f?.filter(e=>"pending"===e.status).length||0,approvedOverrides:f?.filter(e=>"approved"===e.status).length||0,upcomingBookings:h?.length||0,availableDays:m?.filter(e=>e.is_available).length||0};return r.status(200).json({availability:m||[],overrides:f||[],upcomingBookings:h||[],stats:b,pagination:{total:g||0,limit:parseInt(n),offset:parseInt(l),hasMore:parseInt(l)+parseInt(n)<(g||0)},requestId:t})}if("POST"===e.method){let s=e.body;if(!s.artist_id||!s.override_date||!s.override_type)return r.status(400).json({error:"Validation error",message:"Missing required fields: artist_id, override_date, override_type",requestId:t});let{data:i,error:a}=await u.from("artist_profiles").select("id, name, email").eq("id",s.artist_id).single();if(a||!i)return r.status(404).json({error:"Artist not found",message:"The specified artist does not exist",requestId:t});let{data:o,error:d}=await u.from("artist_schedule_overrides").select("id, override_type, status").eq("artist_id",s.artist_id).eq("override_date",s.override_date).single();if(d&&"PGRST116"!==d.code)console.error("Existing override check error:",d);else if(o)return r.status(409).json({error:"Schedule conflict",message:"An override already exists for this date",existingOverride:o,requestId:t});let{data:l}=await u.from("bookings").select("id, start_time, end_time, status").eq("artist_id",s.artist_id).gte("start_time",`${s.override_date}T00:00:00Z`).lt("start_time",`${s.override_date}T23:59:59Z`).in("status",["pending","confirmed"]);if(l&&l.length>0)return r.status(409).json({error:"Booking conflict",message:"There are existing bookings on this date",conflictingBookings:l,requestId:t});let _={id:(0,n.v4)(),artist_id:s.artist_id,override_date:s.override_date,override_type:s.override_type,start_time:s.start_time||null,end_time:s.end_time||null,reason:s.reason||null,notes:s.notes||null,status:"pending",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:c,error:m}=await u.from("artist_schedule_overrides").insert([_]).select(`
          *,
          artist_profiles!inner(
            id,
            name,
            email
          )
        `).single();if(m)return console.error("Schedule override creation error:",m),r.status(500).json({error:"Database error",message:"Failed to create schedule override",requestId:t});return r.status(201).json({override:c,message:"Schedule override created successfully",requestId:t})}if("PUT"===e.method){let{override_id:s}=e.query,i=e.body;if(!s)return r.status(400).json({error:"Validation error",message:"Override ID is required",requestId:t});let{data:a,error:o}=await u.from("artist_schedule_overrides").select("*").eq("id",s).single();if(o||!a)return r.status(404).json({error:"Override not found",message:"The specified schedule override does not exist",requestId:t});i.status&&["approved","denied"].includes(i.status)&&(i.approved_at=new Date().toISOString());let d={...i,updated_at:new Date().toISOString()},{data:n,error:l}=await u.from("artist_schedule_overrides").update(d).eq("id",s).select(`
          *,
          artist_profiles!inner(
            id,
            name,
            email
          ),
          approver:admin_users!approved_by(
            id,
            first_name,
            last_name,
            email
          )
        `).single();if(l)return console.error("Schedule override update error:",l),r.status(500).json({error:"Database error",message:"Failed to update schedule override",requestId:t});return r.status(200).json({override:n,message:"Schedule override updated successfully",requestId:t})}if("DELETE"===e.method){let{override_id:s}=e.query;if(!s)return r.status(400).json({error:"Validation error",message:"Override ID is required",requestId:t});let{data:i,error:a}=await u.from("artist_schedule_overrides").select("id, artist_id, override_date, status").eq("id",s).single();if(a||!i)return r.status(404).json({error:"Override not found",message:"The specified schedule override does not exist",requestId:t});let{error:o}=await u.from("artist_schedule_overrides").delete().eq("id",s);if(o)return console.error("Schedule override deletion error:",o),r.status(500).json({error:"Database error",message:"Failed to delete schedule override",requestId:t});return r.status(200).json({message:"Schedule override deleted successfully",deletedOverride:i,requestId:t})}return r.status(405).json({error:"Method not allowed",message:`HTTP method ${e.method} is not supported`,requestId:t})}catch(e){return console.error("Artist schedule API error:",e),r.status(500).json({error:"Internal server error",message:"An unexpected error occurred",requestId:t})}}let c=(0,o.l)(s,"default"),m=(0,o.l)(s,"config"),p=new i.PagesAPIRouteModule({definition:{kind:a.x.PAGES_API,page:"/api/admin/artists/schedule",pathname:"/api/admin/artists/schedule",bundlePath:"",filename:""},userland:s})},7153:(e,r)=>{var t;Object.defineProperty(r,"x",{enumerable:!0,get:function(){return t}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(t||(t={}))},1802:(e,r,t)=>{e.exports=t(1287)}};var r=require("../../../../webpack-api-runtime.js");r.C(e);var t=r(r.s=155);module.exports=t})();