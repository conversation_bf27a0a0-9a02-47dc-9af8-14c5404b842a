"use strict";(()=>{var e={};e.id=7052,e.ids=[7052],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6555:e=>{e.exports=import("uuid")},8781:(e,r)=>{Object.defineProperty(r,"l",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},7452:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{config:()=>u,default:()=>l,routeModule:()=>_});var s=t(1802),i=t(7153),o=t(8781),d=t(3397),n=e([d]);d=(n.then?(await n)():n)[0];let l=(0,o.l)(d,"default"),u=(0,o.l)(d,"config"),_=new s.PagesAPIRouteModule({definition:{kind:i.x.PAGES_API,page:"/api/admin/artists/schedule",pathname:"/api/admin/artists/schedule",bundlePath:"",filename:""},userland:d});a()}catch(e){a(e)}})},3397:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{default:()=>d});var s=t(2885),i=t(6555),o=e([i]);i=(o.then?(await o)():o)[0];let n=process.env.SUPABASE_SERVICE_ROLE_KEY,l=(0,s.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",n);async function d(e,r){let t=(0,i.v4)();try{let a=e.headers.authorization;if(!a||!a.startsWith("Bearer "))return r.status(401).json({error:"Unauthorized",message:"Valid authentication token required",requestId:t});if("GET"===e.method){let{artist_id:a,override_type:s,status:i,start_date:o,end_date:d,limit:n=50,offset:u=0}=e.query,_=l.from("artist_availability").select(`
          id,
          artist_id,
          day_of_week,
          start_time,
          end_time,
          break_start_time,
          break_end_time,
          is_available,
          created_at,
          artist_profiles!inner(
            id,
            name,
            email,
            is_active
          )
        `).order("day_of_week",{ascending:!0}),c=l.from("artist_schedule_overrides").select(`
          id,
          artist_id,
          override_date,
          override_type,
          start_time,
          end_time,
          reason,
          notes,
          status,
          approved_by,
          approved_at,
          created_by,
          created_at,
          updated_at,
          artist_profiles!inner(
            id,
            name,
            email
          ),
          approver:admin_users!approved_by(
            id,
            first_name,
            last_name,
            email
          ),
          creator:admin_users!created_by(
            id,
            first_name,
            last_name,
            email
          )
        `).order("override_date",{ascending:!1});a&&(_=_.eq("artist_id",a),c=c.eq("artist_id",a)),s&&(c=c.eq("override_type",s)),i&&(c=c.eq("status",i)),o&&(c=c.gte("override_date",o)),d&&(c=c.lte("override_date",d)),c=c.range(parseInt(u),parseInt(u)+parseInt(n)-1);let[{data:m,error:p},{data:f,error:v}]=await Promise.all([_,c]);if(p)return console.error("Availability fetch error:",p),r.status(500).json({error:"Database error",message:"Failed to fetch artist availability",requestId:t});if(v)return console.error("Overrides fetch error:",v),r.status(500).json({error:"Database error",message:"Failed to fetch schedule overrides",requestId:t});let{count:g}=await l.from("artist_schedule_overrides").select("*",{count:"exact",head:!0}),{data:h}=await l.from("bookings").select(`
          id,
          start_time,
          end_time,
          status,
          services(name),
          customers(first_name, last_name)
        `).gte("start_time",new Date().toISOString()).in("status",["pending","confirmed"]).order("start_time",{ascending:!0}).limit(10),y={totalOverrides:f?.length||0,pendingOverrides:f?.filter(e=>"pending"===e.status).length||0,approvedOverrides:f?.filter(e=>"approved"===e.status).length||0,upcomingBookings:h?.length||0,availableDays:m?.filter(e=>e.is_available).length||0};return r.status(200).json({availability:m||[],overrides:f||[],upcomingBookings:h||[],stats:y,pagination:{total:g||0,limit:parseInt(n),offset:parseInt(u),hasMore:parseInt(u)+parseInt(n)<(g||0)},requestId:t})}if("POST"===e.method){let a=e.body;if(!a.artist_id||!a.override_date||!a.override_type)return r.status(400).json({error:"Validation error",message:"Missing required fields: artist_id, override_date, override_type",requestId:t});let{data:s,error:o}=await l.from("artist_profiles").select("id, name, email").eq("id",a.artist_id).single();if(o||!s)return r.status(404).json({error:"Artist not found",message:"The specified artist does not exist",requestId:t});let{data:d,error:n}=await l.from("artist_schedule_overrides").select("id, override_type, status").eq("artist_id",a.artist_id).eq("override_date",a.override_date).single();if(n&&"PGRST116"!==n.code)console.error("Existing override check error:",n);else if(d)return r.status(409).json({error:"Schedule conflict",message:"An override already exists for this date",existingOverride:d,requestId:t});let{data:u}=await l.from("bookings").select("id, start_time, end_time, status").eq("artist_id",a.artist_id).gte("start_time",`${a.override_date}T00:00:00Z`).lt("start_time",`${a.override_date}T23:59:59Z`).in("status",["pending","confirmed"]);if(u&&u.length>0)return r.status(409).json({error:"Booking conflict",message:"There are existing bookings on this date",conflictingBookings:u,requestId:t});let _={id:(0,i.v4)(),artist_id:a.artist_id,override_date:a.override_date,override_type:a.override_type,start_time:a.start_time||null,end_time:a.end_time||null,reason:a.reason||null,notes:a.notes||null,status:"pending",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:c,error:m}=await l.from("artist_schedule_overrides").insert([_]).select(`
          *,
          artist_profiles!inner(
            id,
            name,
            email
          )
        `).single();if(m)return console.error("Schedule override creation error:",m),r.status(500).json({error:"Database error",message:"Failed to create schedule override",requestId:t});return r.status(201).json({override:c,message:"Schedule override created successfully",requestId:t})}if("PUT"===e.method){let{override_id:a}=e.query,s=e.body;if(!a)return r.status(400).json({error:"Validation error",message:"Override ID is required",requestId:t});let{data:i,error:o}=await l.from("artist_schedule_overrides").select("*").eq("id",a).single();if(o||!i)return r.status(404).json({error:"Override not found",message:"The specified schedule override does not exist",requestId:t});s.status&&["approved","denied"].includes(s.status)&&(s.approved_at=new Date().toISOString());let d={...s,updated_at:new Date().toISOString()},{data:n,error:u}=await l.from("artist_schedule_overrides").update(d).eq("id",a).select(`
          *,
          artist_profiles!inner(
            id,
            name,
            email
          ),
          approver:admin_users!approved_by(
            id,
            first_name,
            last_name,
            email
          )
        `).single();if(u)return console.error("Schedule override update error:",u),r.status(500).json({error:"Database error",message:"Failed to update schedule override",requestId:t});return r.status(200).json({override:n,message:"Schedule override updated successfully",requestId:t})}if("DELETE"===e.method){let{override_id:a}=e.query;if(!a)return r.status(400).json({error:"Validation error",message:"Override ID is required",requestId:t});let{data:s,error:i}=await l.from("artist_schedule_overrides").select("id, artist_id, override_date, status").eq("id",a).single();if(i||!s)return r.status(404).json({error:"Override not found",message:"The specified schedule override does not exist",requestId:t});let{error:o}=await l.from("artist_schedule_overrides").delete().eq("id",a);if(o)return console.error("Schedule override deletion error:",o),r.status(500).json({error:"Database error",message:"Failed to delete schedule override",requestId:t});return r.status(200).json({message:"Schedule override deleted successfully",deletedOverride:s,requestId:t})}return r.status(405).json({error:"Method not allowed",message:`HTTP method ${e.method} is not supported`,requestId:t})}catch(e){return console.error("Artist schedule API error:",e),r.status(500).json({error:"Internal server error",message:"An unexpected error occurred",requestId:t})}}a()}catch(e){a(e)}})},7153:(e,r)=>{var t;Object.defineProperty(r,"x",{enumerable:!0,get:function(){return t}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(t||(t={}))},1802:(e,r,t)=>{e.exports=t(1287)}};var r=require("../../../../webpack-api-runtime.js");r.C(e);var t=r(r.s=7452);module.exports=t})();