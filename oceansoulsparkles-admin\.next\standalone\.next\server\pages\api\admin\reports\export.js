"use strict";(()=>{var e={};e.id=522,e.ids=[522],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},5158:e=>{e.exports=require("jspdf")},5464:e=>{e.exports=require("jspdf-autotable")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},6302:e=>{e.exports=require("xlsx")},8731:(e,t,o)=>{o.r(t),o.d(t,{config:()=>w,default:()=>p,routeModule:()=>y});var r={};o.r(r),o.d(r,{default:()=>b});var n=o(1802),a=o(7153),s=o(8781),l=o(7474);let i=o(5158).jsPDF;async function u(e,t,o){let r=new i;r.setProperties({title:"Ocean Soul Sparkles - Business Report",subject:`Business Analytics Report - ${o}`,author:"Ocean Soul Sparkles Admin System",creator:"Ocean Soul Sparkles Admin Dashboard"}),r.setFillColor(102,126,234),r.rect(20,10,170,30,"F"),r.setTextColor(255,255,255),r.setFontSize(20),r.setFont("helvetica","bold"),r.text("Ocean Soul Sparkles",25,25),r.setFontSize(14),r.setFont("helvetica","normal"),r.text("Business Analytics Report",25,35),r.setTextColor(0,0,0),r.setFontSize(12),r.text(`Report Period: ${{last7days:"Last 7 Days",last30days:"Last 30 Days",last90days:"Last 90 Days",thisyear:"This Year"}[o]||o}`,20,50),r.text(`Generated: ${new Date().toLocaleDateString("en-AU")}`,120,50);let n=60;return(n=function(e,t,o){e.setFontSize(16),e.setFont("helvetica","bold"),e.setTextColor(102,126,234),e.text("Executive Summary",20,o),o+=15;let r=[["Total Revenue",c(t.totalRevenue),`${t.revenueGrowth>=0?"+":""}${t.revenueGrowth.toFixed(1)}%`],["Total Bookings",t.totalBookings.toString(),`${t.bookingGrowth>=0?"+":""}${t.bookingGrowth.toFixed(1)}%`],["Total Customers",t.totalCustomers.toString(),"-"],["Average Booking Value",c(t.averageBookingValue),"-"]];return e.autoTable({startY:o,head:[["Metric","Value","Growth"]],body:r,theme:"grid",headStyles:{fillColor:[102,126,234],textColor:[255,255,255],fontStyle:"bold"},styles:{fontSize:10,cellPadding:5},columnStyles:{0:{cellWidth:60},1:{cellWidth:50,halign:"right"},2:{cellWidth:30,halign:"center"}}}),e.lastAutoTable.finalY+20}(r,e.overview,n))>200&&(r.addPage(),n=20),(n=function(e,t,o){if(e.setFontSize(16),e.setFont("helvetica","bold"),e.setTextColor(102,126,234),e.text("Revenue Analysis",20,o),o+=15,t.byService&&t.byService.length>0){e.setFontSize(12),e.setFont("helvetica","bold"),e.setTextColor(0,0,0),e.text("Revenue by Service",20,o),o+=10;let r=t.byService.map(e=>[e.service,c(e.amount),`${e.percentage.toFixed(1)}%`]);e.autoTable({startY:o,head:[["Service","Revenue","Percentage"]],body:r,theme:"striped",headStyles:{fillColor:[118,75,162],textColor:[255,255,255],fontStyle:"bold"},styles:{fontSize:9,cellPadding:4},columnStyles:{0:{cellWidth:80},1:{cellWidth:40,halign:"right"},2:{cellWidth:30,halign:"center"}}}),o=e.lastAutoTable.finalY+15}return o}(r,e.revenue,n))>200&&(r.addPage(),n=20),(n=function(e,t,o){if(e.setFontSize(16),e.setFont("helvetica","bold"),e.setTextColor(102,126,234),e.text("Booking Analysis",20,o),o+=15,t.statusBreakdown&&t.statusBreakdown.length>0){e.setFontSize(12),e.setFont("helvetica","bold"),e.setTextColor(0,0,0),e.text("Booking Status Breakdown",20,o),o+=10;let r=t.statusBreakdown.map(e=>[e.status,e.count.toString(),`${e.percentage.toFixed(1)}%`]);e.autoTable({startY:o,head:[["Status","Count","Percentage"]],body:r,theme:"striped",headStyles:{fillColor:[34,197,94],textColor:[255,255,255],fontStyle:"bold"},styles:{fontSize:9,cellPadding:4},columnStyles:{0:{cellWidth:60},1:{cellWidth:30,halign:"center"},2:{cellWidth:30,halign:"center"}}}),o=e.lastAutoTable.finalY+10}return e.setFontSize(10),e.setFont("helvetica","normal"),e.text(`Cancellation Rate: ${t.cancellationRate.toFixed(1)}%`,20,o),o+15}(r,e.bookings,n))>200&&(r.addPage(),n=20),n=function(e,t,o){e.setFontSize(16),e.setFont("helvetica","bold"),e.setTextColor(102,126,234),e.text("Customer Analysis",20,o),o+=15;let r=[["New Customers",t.newCustomers.toString()],["Returning Customers",t.returningCustomers.toString()],["Customer Lifetime Value",c(t.customerLifetimeValue)]];return e.autoTable({startY:o,head:[["Metric","Value"]],body:r,theme:"grid",headStyles:{fillColor:[59,130,246],textColor:[255,255,255],fontStyle:"bold"},styles:{fontSize:10,cellPadding:5},columnStyles:{0:{cellWidth:80},1:{cellWidth:50,halign:"right"}}}),e.lastAutoTable.finalY+20}(r,e.customers,n),function(e,t){let o=e.internal.getNumberOfPages();for(let r=1;r<=o;r++)e.setPage(r),e.setDrawColor(200,200,200),e.line(20,280,190,280),e.setFontSize(8),e.setFont("helvetica","normal"),e.setTextColor(100,100,100),e.text("Ocean Soul Sparkles - Confidential Business Report",20,285),e.text(`Page ${r} of ${o}`,160,285),e.text(`Generated: ${new Date(t.generatedAt).toLocaleString("en-AU")}`,20,290)}(r,e.metadata),r.output("arraybuffer")}function c(e){return new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e)}o(5464);let d=o(6302);async function g(e,t,o){let r=d.utils.book_new();return function(e,t,o){let r=[["Ocean Soul Sparkles - Business Report"],[`Report Period: ${{last7days:"Last 7 Days",last30days:"Last 30 Days",last90days:"Last 90 Days",thisyear:"This Year"}[o]||o}`],[`Generated: ${new Date().toLocaleDateString("en-AU")}`],[],["Executive Summary"],[],["Metric","Value","Growth"],["Total Revenue",m(t.totalRevenue),`${t.revenueGrowth>=0?"+":""}${t.revenueGrowth.toFixed(1)}%`],["Total Bookings",t.totalBookings,`${t.bookingGrowth>=0?"+":""}${t.bookingGrowth.toFixed(1)}%`],["Total Customers",t.totalCustomers,"-"],["Average Booking Value",m(t.averageBookingValue),"-"]],n=d.utils.aoa_to_sheet(r);n["!cols"]=[{width:25},{width:20},{width:15}],n.A1&&(n.A1.s={font:{bold:!0,sz:16,color:{rgb:"FFFFFF"}},fill:{fgColor:{rgb:"667EEA"}},alignment:{horizontal:"center"}}),n.A5&&(n.A5.s={font:{bold:!0,sz:14},fill:{fgColor:{rgb:"F3F4F6"}}}),["A7","B7","C7"].forEach(e=>{n[e]&&(n[e].s={font:{bold:!0,color:{rgb:"FFFFFF"}},fill:{fgColor:{rgb:"667EEA"}},alignment:{horizontal:"center"}})}),d.utils.book_append_sheet(e,n,"Overview")}(r,e.overview,o),function(e,t){let o=[["Revenue Analysis"],[],["Revenue by Service"],["Service","Revenue","Percentage"],...t.byService.map(e=>[e.service,e.amount,`${e.percentage.toFixed(1)}%`]),[],["Daily Revenue"],["Date","Amount"],...t.daily.map(e=>[new Date(e.date).toLocaleDateString("en-AU"),e.amount])],r=d.utils.aoa_to_sheet(o);r["!cols"]=[{width:25},{width:15},{width:15}],r.A1&&(r.A1.s={font:{bold:!0,sz:14},fill:{fgColor:{rgb:"764BA2"}},font:{color:{rgb:"FFFFFF"}}}),r.A3&&(r.A3.s={font:{bold:!0,sz:12},fill:{fgColor:{rgb:"F3F4F6"}}}),["A4","B4","C4"].forEach(e=>{r[e]&&(r[e].s={font:{bold:!0,color:{rgb:"FFFFFF"}},fill:{fgColor:{rgb:"764BA2"}},alignment:{horizontal:"center"}})});let n=7+t.byService.length;r[`A${n}`]&&(r[`A${n}`].s={font:{bold:!0,sz:12},fill:{fgColor:{rgb:"F3F4F6"}}}),[`A${n+1}`,`B${n+1}`].forEach(e=>{r[e]&&(r[e].s={font:{bold:!0,color:{rgb:"FFFFFF"}},fill:{fgColor:{rgb:"764BA2"}},alignment:{horizontal:"center"}})}),d.utils.book_append_sheet(e,r,"Revenue")}(r,e.revenue),function(e,t){let o=[["Booking Analysis"],[],["Booking Status Breakdown"],["Status","Count","Percentage"],...t.statusBreakdown.map(e=>[e.status,e.count,`${e.percentage.toFixed(1)}%`]),[],["Summary Statistics"],["Metric","Value"],["Total Bookings",t.statusBreakdown.reduce((e,t)=>e+t.count,0)],["Cancellation Rate",`${t.cancellationRate.toFixed(1)}%`],["Success Rate",`${(100-t.cancellationRate).toFixed(1)}%`]],r=d.utils.aoa_to_sheet(o);r["!cols"]=[{width:20},{width:15},{width:15}],r.A1&&(r.A1.s={font:{bold:!0,sz:14,color:{rgb:"FFFFFF"}},fill:{fgColor:{rgb:"22C55E"}}}),r.A3&&(r.A3.s={font:{bold:!0,sz:12},fill:{fgColor:{rgb:"F3F4F6"}}}),["A4","B4","C4"].forEach(e=>{r[e]&&(r[e].s={font:{bold:!0,color:{rgb:"FFFFFF"}},fill:{fgColor:{rgb:"22C55E"}},alignment:{horizontal:"center"}})});let n=7+t.statusBreakdown.length;r[`A${n}`]&&(r[`A${n}`].s={font:{bold:!0,sz:12},fill:{fgColor:{rgb:"F3F4F6"}}}),[`A${n+1}`,`B${n+1}`].forEach(e=>{r[e]&&(r[e].s={font:{bold:!0,color:{rgb:"FFFFFF"}},fill:{fgColor:{rgb:"22C55E"}},alignment:{horizontal:"center"}})}),d.utils.book_append_sheet(e,r,"Bookings")}(r,e.bookings),function(e,t){let o=[["Customer Analysis"],[],["Customer Metrics"],["Metric","Value"],["New Customers",t.newCustomers],["Returning Customers",t.returningCustomers],["Customer Lifetime Value",m(t.customerLifetimeValue)],["Retention Rate",`${(t.returningCustomers/(t.newCustomers+t.returningCustomers)*100).toFixed(1)}%`],[],["Customer Distribution"],["Type","Count","Percentage"],["New Customers",t.newCustomers,`${(t.newCustomers/(t.newCustomers+t.returningCustomers)*100).toFixed(1)}%`],["Returning Customers",t.returningCustomers,`${(t.returningCustomers/(t.newCustomers+t.returningCustomers)*100).toFixed(1)}%`]],r=d.utils.aoa_to_sheet(o);r["!cols"]=[{width:25},{width:15},{width:15}],r.A1&&(r.A1.s={font:{bold:!0,sz:14,color:{rgb:"FFFFFF"}},fill:{fgColor:{rgb:"3B82F6"}}}),r.A3&&(r.A3.s={font:{bold:!0,sz:12},fill:{fgColor:{rgb:"F3F4F6"}}}),r.A10&&(r.A10.s={font:{bold:!0,sz:12},fill:{fgColor:{rgb:"F3F4F6"}}}),["A4","B4"].forEach(e=>{r[e]&&(r[e].s={font:{bold:!0,color:{rgb:"FFFFFF"}},fill:{fgColor:{rgb:"3B82F6"}},alignment:{horizontal:"center"}})}),["A11","B11","C11"].forEach(e=>{r[e]&&(r[e].s={font:{bold:!0,color:{rgb:"FFFFFF"}},fill:{fgColor:{rgb:"3B82F6"}},alignment:{horizontal:"center"}})}),d.utils.book_append_sheet(e,r,"Customers")}(r,e.customers),d.write(r,{type:"buffer",bookType:"xlsx",compression:!0})}function m(e){return new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e)}async function f(e,t,o){var r,n;let a,s="";return s+=`Ocean Soul Sparkles - Business Report
Report Period: ${({last7days:"Last 7 Days",last30days:"Last 30 Days",last90days:"Last 90 Days",thisyear:"This Year"})[o]||o}
Generated: ${new Date().toLocaleDateString("en-AU")}

`+(r=e.overview,`EXECUTIVE SUMMARY
Metric,Value,Growth
Total Revenue,${F(r.totalRevenue)},${r.revenueGrowth>=0?"+":""}${r.revenueGrowth.toFixed(1)}%
Total Bookings,${r.totalBookings},${r.bookingGrowth>=0?"+":""}${r.bookingGrowth.toFixed(1)}%
Total Customers,${r.totalCustomers},-
Average Booking Value,${F(r.averageBookingValue)},-

`)+(n=e.revenue,a="REVENUE ANALYSIS\n\nRevenue by Service\nService,Revenue,Percentage\n",n.byService.forEach(e=>{a+=`"${e.service}",${e.amount.toFixed(2)},${e.percentage.toFixed(1)}%
`}),a+="\nDaily Revenue\nDate,Amount\n",n.daily.forEach(e=>{let t=new Date(e.date).toLocaleDateString("en-AU");a+=`${t},${e.amount.toFixed(2)}
`}),a+="\n")+function(e){let t="BOOKING ANALYSIS\n\n";t+="Booking Status Breakdown\nStatus,Count,Percentage\n",e.statusBreakdown.forEach(e=>{t+=`${e.status},${e.count},${e.percentage.toFixed(1)}%
`}),t+="\nSummary Statistics\nMetric,Value\n";let o=e.statusBreakdown.reduce((e,t)=>e+t.count,0);return t+=`Total Bookings,${o}
Cancellation Rate,${e.cancellationRate.toFixed(1)}%
Success Rate,${(100-e.cancellationRate).toFixed(1)}%

`}(e.bookings)+function(e){let t="CUSTOMER ANALYSIS\n\n";t+=`Customer Metrics
Metric,Value
New Customers,${e.newCustomers}
Returning Customers,${e.returningCustomers}
Customer Lifetime Value,${F(e.customerLifetimeValue)}
`;let o=e.newCustomers+e.returningCustomers,r=o>0?e.returningCustomers/o*100:0;t+=`Retention Rate,${r.toFixed(1)}%

Customer Distribution
Type,Count,Percentage
`;let n=o>0?e.newCustomers/o*100:0,a=o>0?e.returningCustomers/o*100:0;return t+`New Customers,${e.newCustomers},${n.toFixed(1)}%
Returning Customers,${e.returningCustomers},${a.toFixed(1)}%

`}(e.customers),Buffer.from(s,"utf8")}function F(e){return new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e)}var h=o(6482);async function b(e,t){let o=`export-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;try{let r,n,a;if(console.log(`[${o}] Export request started:`,{method:e.method,query:e.query,userAgent:e.headers["user-agent"]}),"GET"!==e.method)return t.status(405).json({error:"Method not allowed",requestId:o});let s=await (0,l.Wg)(e);if(!s.valid)return console.log(`[${o}] Authentication failed:`,s.error),t.status(401).json({error:"Unauthorized",requestId:o});if(!["DEV","Admin"].includes(s.user.role))return console.log(`[${o}] Insufficient permissions:`,s.user.role),t.status(403).json({error:"Insufficient permissions",requestId:o});let{format:i="pdf",range:c="last30days",type:d="all"}=e.query;if(!["pdf","excel","csv"].includes(i))return t.status(400).json({error:"Invalid format. Supported formats: pdf, excel, csv",requestId:o});let m=function(e){let t;let o=new Date;switch(e){case"last7days":t=new Date(o.getTime()-6048e5);break;case"last30days":default:t=new Date(o.getTime()-2592e6);break;case"last90days":t=new Date(o.getTime()-7776e6);break;case"thisyear":t=new Date(o.getFullYear(),0,1)}return{start:t.toISOString(),end:o.toISOString(),label:e}}(c),F=await C(m,d,o);switch(i){case"pdf":r=await u(F,m,c),n="application/pdf",a="pdf";break;case"excel":r=await g(F,m,c),n="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",a="xlsx";break;case"csv":r=await f(F,m,c),n="text/csv",a="csv"}let h=`ocean-soul-sparkles-report-${c}-${Date.now()}.${a}`;return t.setHeader("Content-Type",n),t.setHeader("Content-Disposition",`attachment; filename="${h}"`),t.setHeader("Cache-Control","no-cache"),console.log(`[${o}] Export completed successfully:`,{format:i,range:c,filename:h,dataSize:r.length}),t.send(r)}catch(e){return console.error(`[${o}] Export error:`,e),t.status(500).json({error:"Export failed",message:e.message,requestId:o})}}async function C(e,t,o){try{let t={overview:{},revenue:{},bookings:{},customers:{},metadata:{generatedAt:new Date().toISOString(),dateRange:e,requestId:o}},{data:r,error:n}=await h.supabaseAdmin.from("bookings").select(`
        id,
        total_amount,
        status,
        created_at,
        customer_id,
        services (name),
        artists (first_name, last_name)
      `).gte("created_at",e.start).lte("created_at",e.end);n&&console.warn(`[${o}] Bookings query error:`,n);let{data:a,error:s}=await h.supabaseAdmin.from("customers").select("id, first_name, last_name, email, created_at").gte("created_at",e.start).lte("created_at",e.end);s&&console.warn(`[${o}] Customers query error:`,s);let l=r?.length||0,i=r?.reduce((e,t)=>e+(t.total_amount||0),0)||0,u=a?.length||0,c=l>0?i/l:0;t.overview={totalRevenue:i,totalBookings:l,totalCustomers:u,averageBookingValue:c,revenueGrowth:12.5,bookingGrowth:8.3};let d={},g={};r?.forEach(e=>{if("completed"===e.status){let t=e.total_amount||0,o=e.services?.name||"Unknown Service",r=e.created_at.split("T")[0];d[o]?d[o]+=t:d[o]=t,g[r]?g[r]+=t:g[r]=t}});let m=Object.values(d).reduce((e,t)=>e+t,0);t.revenue={daily:Object.entries(g).map(([e,t])=>({date:e,amount:t})),byService:Object.entries(d).map(([e,t])=>({service:e,amount:t,percentage:m>0?t/m*100:0}))};let f={};r?.forEach(e=>{let t=e.status||"Unknown";f[t]?f[t]++:f[t]=1});let F=f.cancelled||0,b=l>0?F/l*100:0;return t.bookings={statusBreakdown:Object.entries(f).map(([e,t])=>({status:e,count:t,percentage:l>0?t/l*100:0})),cancellationRate:b},t.customers={newCustomers:u,returningCustomers:Math.floor(.6*u),customerLifetimeValue:1.5*c},t}catch(e){throw console.error(`[${o}] Error fetching report data:`,e),e}}let p=(0,s.l)(r,"default"),w=(0,s.l)(r,"config"),y=new n.PagesAPIRouteModule({definition:{kind:a.x.PAGES_API,page:"/api/admin/reports/export",pathname:"/api/admin/reports/export",bundlePath:"",filename:""},userland:r})},6482:(e,t,o)=>{o.r(t),o.d(t,{supabaseAdmin:()=>s});var r=o(2885);let n="https://ndlgbcsbidyhxbpqzgqp.supabase.co",a=process.env.SUPABASE_SERVICE_ROLE_KEY;n&&a||console.warn("Missing Supabase environment variables for admin client");let s=(0,r.createClient)(n,a||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",{auth:{autoRefreshToken:!1,persistSession:!1}})}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var o=e=>t(t.s=e),r=t.X(0,[2805],()=>o(8731));module.exports=r})();