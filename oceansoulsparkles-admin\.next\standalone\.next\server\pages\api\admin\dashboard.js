"use strict";(()=>{var e={};e.id=4438,e.ids=[4438],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},195:(e,t,a)=>{a.r(t),a.d(t,{config:()=>p,default:()=>m,routeModule:()=>h});var o={};a.r(o),a.d(o,{default:()=>u});var n=a(1802),r=a(7153),i=a(8781),s=a(7474),l=a(2885);let d=process.env.SUPABASE_SERVICE_ROLE_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder",c=(0,l.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",d);async function u(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let a=e.headers.authorization?.replace("Bearer ","")||e.cookies["admin-token"];if(!a)return t.status(401).json({error:"No authentication token"});let o=await (0,s.Wg)(a);if(!o.valid||!o.user)return t.status(401).json({error:"Invalid authentication"});let n=o.user,r=await g(n.role,n.id);return t.status(200).json(r)}catch(e){return console.error("Dashboard API error:",e),t.status(500).json({error:"Internal server error"})}}async function g(e,t){let a=new Date,o=new Date(a.getFullYear(),a.getMonth(),1),n=new Date(a.getFullYear(),a.getMonth()-1,1),r=new Date(a.getFullYear(),a.getMonth(),0);try{let a={totalBookings:0,totalRevenue:0,activeCustomers:0,pendingBookings:0,completedBookings:0,cancelledBookings:0,averageBookingValue:0,monthlyGrowth:0},i=[],s=[],l=null,d=null;if("DEV"===e||"Admin"===e){let{data:e}=await c.from("bookings").select("*").gte("created_at",o.toISOString());e&&(a.totalBookings=e.length,a.totalRevenue=e.filter(e=>"completed"===e.status).reduce((e,t)=>e+(t.total_amount||0),0),a.pendingBookings=e.filter(e=>"pending"===e.status).length,a.completedBookings=e.filter(e=>"completed"===e.status).length,a.cancelledBookings=e.filter(e=>"cancelled"===e.status).length,a.averageBookingValue=a.totalBookings>0?a.totalRevenue/a.completedBookings:0);let{count:t}=await c.from("customers").select("*",{count:"exact",head:!0}).gte("updated_at",new Date(Date.now()-2592e6).toISOString());a.activeCustomers=t||0;let{data:n}=await c.from("bookings").select(`
          id,
          booking_date,
          booking_time,
          status,
          total_amount,
          created_at,
          customer_id,
          service_id,
          artist_id,
          customers!inner(id, first_name, last_name),
          services!inner(id, name, duration),
          user_profiles!artist_id(id, name)
        `).order("created_at",{ascending:!1}).limit(10);i=n||[],d={avgResponseTime:150,activeUsers:5,systemHealth:"healthy"}}else if("Artist"===e||"Braider"===e){let{data:e}=await c.from("bookings").select("*").eq("artist_id",t).gte("created_at",o.toISOString());e&&(a.totalBookings=e.length,a.totalRevenue=e.filter(e=>"completed"===e.status).reduce((e,t)=>e+(t.total_amount||0),0),a.pendingBookings=e.filter(e=>"pending"===e.status).length,a.completedBookings=e.filter(e=>"completed"===e.status).length,a.cancelledBookings=e.filter(e=>"cancelled"===e.status).length);let{data:n}=await c.from("bookings").select("*").eq("artist_id",t).gte("created_at",new Date(Date.now()-6048e5).toISOString()),{data:r}=await c.from("bookings").select("*").eq("artist_id",t).gte("created_at",o.toISOString()).eq("status","completed");l={weeklyBookings:n?.length||0,monthlyEarnings:r?.reduce((e,t)=>e+(t.total_amount||0),0)||0,averageRating:4.8};let{data:s}=await c.from("bookings").select(`
          *,
          customers (first_name, last_name),
          services (name)
        `).eq("artist_id",t).order("created_at",{ascending:!1}).limit(10);i=s||[]}let{data:u}=await c.from("bookings").select("total_amount").gte("created_at",n.toISOString()).lte("created_at",r.toISOString()).eq("status","completed"),g=u?.reduce((e,t)=>e+(t.total_amount||0),0)||0;return g>0&&(a.monthlyGrowth=(a.totalRevenue-g)/g*100),s=[{id:"1",type:"booking",title:"New booking created",description:"Sarah Johnson booked Festival Face Paint",timestamp:new Date(Date.now()-18e5).toISOString(),user:"Emma Wilson"},{id:"2",type:"payment",title:"Payment received",description:"Payment of $120 received",timestamp:new Date(Date.now()-27e5).toISOString(),user:"System"},{id:"3",type:"customer",title:"New customer registered",description:"Mike Chen created account",timestamp:new Date(Date.now()-36e5).toISOString(),user:"Mike Chen"}],{stats:a,recentBookings:i,recentActivity:s,artistStats:l,systemStats:d}}catch(e){return console.error("Error fetching dashboard data:",e),{stats:{totalBookings:0,totalRevenue:0,activeCustomers:0,pendingBookings:0,completedBookings:0,cancelledBookings:0,averageBookingValue:0,monthlyGrowth:0},recentBookings:[],recentActivity:[],artistStats:null,systemStats:null}}}let m=(0,i.l)(o,"default"),p=(0,i.l)(o,"config"),h=new n.PagesAPIRouteModule({definition:{kind:r.x.PAGES_API,page:"/api/admin/dashboard",pathname:"/api/admin/dashboard",bundlePath:"",filename:""},userland:o})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var a=e=>t(t.s=e),o=t.X(0,[2805],()=>a(195));module.exports=o})();