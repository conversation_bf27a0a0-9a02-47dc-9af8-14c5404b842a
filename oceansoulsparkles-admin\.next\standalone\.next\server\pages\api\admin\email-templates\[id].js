"use strict";(()=>{var e={};e.id=2356,e.ids=[2356],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},8052:(e,t,a)=>{a.r(t),a.d(t,{config:()=>c,default:()=>p,routeModule:()=>f});var s={};a.r(s),a.d(s,{default:()=>d});var r=a(1802),i=a(7153),n=a(8781),o=a(7474),l=a(2885);let u=process.env.SUPABASE_SERVICE_ROLE_KEY,m=(0,l.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",u);async function d(e,t){let{id:a}=e.query,s=Math.random().toString(36).substring(2,8);if(console.log(`[${s}] Email template ${a} API called - ${e.method}`),!a||"string"!=typeof a)return t.status(400).json({error:"Template ID is required",requestId:s});try{let r=await (0,o.SA)(e);if(!r.success)return t.status(401).json({error:"Authentication required",message:r.message||"Authentication failed",requestId:s});let{user:i}=r;if(!i)return t.status(401).json({error:"User not found",requestId:s});if("Admin"!==i.role&&"DEV"!==i.role)return t.status(403).json({error:"Insufficient permissions",message:"Only admins can manage email templates",requestId:s});if("GET"===e.method){let{data:e,error:r}=await m.from("email_templates").select(`
          id,
          name,
          type,
          subject,
          html_content,
          text_content,
          variables,
          is_active,
          is_default,
          created_at,
          updated_at
        `).eq("id",a).single();if(r)return console.error(`[${s}] Database error:`,r),t.status(500).json({error:"Failed to fetch email template",message:r.message,requestId:s});if(!e)return t.status(404).json({error:"Template not found",requestId:s});return t.status(200).json({template:e,requestId:s})}if("PUT"===e.method){let{name:r,type:n,subject:o,html_content:l,text_content:u,variables:d,is_active:p,is_default:c}=e.body;if(!r||!n||!o||!l)return t.status(400).json({error:"Missing required fields",message:"Name, type, subject, and HTML content are required",requestId:s});let{data:f}=await m.from("email_templates").select("id").eq("name",r).neq("id",a).single();if(f)return t.status(409).json({error:"Template name already exists",message:"A template with this name already exists",requestId:s});c&&await m.from("email_templates").update({is_default:!1}).eq("type",n).neq("id",a);let{data:g,error:j}=await m.from("email_templates").update({name:r,type:n,subject:o,html_content:l,text_content:u,variables:d||[],is_active:p,is_default:c,updated_by:i.id,updated_at:new Date().toISOString()}).eq("id",a).select().single();if(j)return console.error(`[${s}] Error updating template:`,j),t.status(500).json({error:"Failed to update email template",message:j.message,requestId:s});return t.status(200).json({template:g,message:"Email template updated successfully",requestId:s})}if("DELETE"===e.method){let{count:e}=await m.from("customer_communications").select("*",{count:"exact",head:!0}).eq("template_id",a);if(e&&e>0)return t.status(400).json({error:"Cannot delete template",message:`This template has been used in ${e} communication(s). Deactivate it instead.`,requestId:s});let{error:r}=await m.from("email_templates").delete().eq("id",a);if(r)return console.error(`[${s}] Error deleting template:`,r),t.status(500).json({error:"Failed to delete email template",message:r.message,requestId:s});return t.status(200).json({message:"Email template deleted successfully",requestId:s})}return t.status(405).json({error:"Method not allowed",requestId:s})}catch(e){return console.error(`[${s}] Unexpected error:`,e),t.status(500).json({error:"Internal server error",message:e instanceof Error?e.message:"Unknown error",requestId:s})}}let p=(0,n.l)(s,"default"),c=(0,n.l)(s,"config"),f=new r.PagesAPIRouteModule({definition:{kind:i.x.PAGES_API,page:"/api/admin/email-templates/[id]",pathname:"/api/admin/email-templates/[id]",bundlePath:"",filename:""},userland:s})}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[2805],()=>a(8052));module.exports=s})();