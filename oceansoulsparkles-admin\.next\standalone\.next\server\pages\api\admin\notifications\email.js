"use strict";(()=>{var e={};e.id=1263,e.ids=[1263],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5184:e=>{e.exports=require("nodemailer")},9200:e=>{e.exports=require("speakeasy")},5254:(e,t,o)=>{o.r(t),o.d(t,{config:()=>u,default:()=>d,routeModule:()=>m});var r={};o.r(r),o.d(r,{default:()=>p});var n=o(1802),a=o(7153),s=o(8781),i=o(7474),l=o(5606),c=o.n(l);async function p(e,t){let o=Math.random().toString(36).substring(2,8);console.log(`[${o}] Email notifications API called - ${e.method}`);try{let{user:r,error:n}=await (0,i.ZQ)(e);if(n||!r)return t.status(401).json({error:"Authentication required",message:n?.message||"Authentication failed",requestId:o});if("POST"===e.method){let r;let{type:n,data:a}=e.body;if(!n)return t.status(400).json({error:"Email type is required",requestId:o});switch(n){case"booking_confirmation":r=await c().sendBookingConfirmation(a);break;case"booking_reminder":r=await c().sendBookingReminder(a);break;case"booking_cancellation":r=await c().sendBookingCancellation(a);break;case"payment_receipt":r=await c().sendPaymentReceipt(a);break;case"staff_notification":r=await c().sendStaffNotification(a);break;case"low_inventory_alert":r=await c().sendLowInventoryAlert(a.items,a.adminEmail);break;case"test_email":if(!a.to)return t.status(400).json({error:"Recipient email is required for test email",requestId:o});r=await c().sendTest(a.to);break;default:return t.status(400).json({error:`Unknown email type: ${n}`,requestId:o})}if(r.success)return console.log(`[${o}] Email sent successfully:`,r.messageId),t.status(200).json({success:!0,messageId:r.messageId,message:"Email sent successfully",requestId:o});return console.error(`[${o}] Email sending failed:`,r.error),t.status(500).json({error:"Failed to send email",message:r.error,requestId:o})}if("GET"===e.method){let e=c().getStatus();return t.status(200).json({status:e,requestId:o})}return t.status(405).json({error:"Method not allowed",requestId:o})}catch(e){return console.error(`[${o}] Email API error:`,e),t.status(500).json({error:"Internal server error",message:e.message,requestId:o})}}let d=(0,s.l)(r,"default"),u=(0,s.l)(r,"config"),m=new n.PagesAPIRouteModule({definition:{kind:a.x.PAGES_API,page:"/api/admin/notifications/email",pathname:"/api/admin/notifications/email",bundlePath:"",filename:""},userland:r})},5606:(e,t,o)=>{let{sendEmail:r,verifyConnection:n,sendTestEmail:a}=o(3353),{bookingConfirmationTemplate:s,bookingReminderTemplate:i,bookingCancellationTemplate:l,paymentReceiptTemplate:c,staffNotificationTemplate:p,lowInventoryAlertTemplate:d}=o(3306);class u{constructor(){this.isConfigured=!!(process.env.SMTP_USER&&process.env.SMTP_PASS)}async checkEmailEnabled(e=null){try{let t=await fetch(`${process.env.NEXT_PUBLIC_BASE_URL||"http://localhost:3000"}/api/admin/settings`),{settings:o}=await t.json(),r=o?.notifications||{};if(!r.emailNotifications)return{enabled:!1,reason:"Email notifications disabled globally"};if(e){let t=`email${e.charAt(0).toUpperCase()+e.slice(1)}`;if(!1===r[t])return{enabled:!1,reason:`Email ${e} notifications disabled`}}return{enabled:!0}}catch(e){return console.error("Error checking email settings:",e),{enabled:!0}}}async sendBookingConfirmation(e){let t=await this.checkEmailEnabled("bookingConfirmation");if(!t.enabled)return console.log(`Email booking confirmation skipped: ${t.reason}`),{success:!1,skipped:!0,reason:t.reason};if(!e.customerEmail)return console.warn("No customer email provided for booking confirmation"),{success:!1,error:"No customer email"};let o=s(e);return await r({to:e.customerEmail,subject:`Booking Confirmation - ${e.serviceName}`,html:o})}async sendBookingReminder(e){let t=await this.checkEmailEnabled("bookingReminder");if(!t.enabled)return console.log(`Email booking reminder skipped: ${t.reason}`),{success:!1,skipped:!0,reason:t.reason};if(!e.customerEmail)return console.warn("No customer email provided for booking reminder"),{success:!1,error:"No customer email"};let o=i(e);return await r({to:e.customerEmail,subject:`Appointment Reminder - Tomorrow at ${e.time}`,html:o})}async sendBookingCancellation(e){if(!e.customerEmail)return console.warn("No customer email provided for booking cancellation"),{success:!1,error:"No customer email"};let t=l(e);return await r({to:e.customerEmail,subject:`Booking Cancellation - ${e.serviceName}`,html:t})}async sendPaymentReceipt(e){if(!e.customerEmail)return console.warn("No customer email provided for payment receipt"),{success:!1,error:"No customer email"};let t=c(e);return await r({to:e.customerEmail,subject:`Payment Receipt - ${e.receiptNumber}`,html:t})}async sendStaffNotification(e){if(!e.staffEmail)return console.warn("No staff email provided for notification"),{success:!1,error:"No staff email"};let t=p(e);return await r({to:e.staffEmail,subject:e.subject||"Staff Notification",html:t})}async sendLowInventoryAlert(e,t){t||(t=process.env.ADMIN_EMAIL||"<EMAIL>");let o=d(e);return await r({to:t,subject:`Low Inventory Alert - ${e.length} items need attention`,html:o})}async sendBulkEmail(e,t,o){let n=[];for(let a of e)try{let e=await r({to:a.email,subject:t,html:o.replace(/{{name}}/g,a.name||"Valued Customer")});n.push({email:a.email,...e}),await new Promise(e=>setTimeout(e,1e3))}catch(e){n.push({email:a.email,success:!1,error:e.message})}return n}async verifyConfiguration(){return await n()}async sendTest(e){return await a(e)}getStatus(){return{configured:this.isConfigured,smtpHost:process.env.SMTP_HOST||"Not configured",smtpUser:process.env.SMTP_USER?"Configured":"Not configured",smtpPort:process.env.SMTP_PORT||"587"}}}let m=new u;e.exports=m},3353:(e,t,o)=>{let r=o(5184);function n(){let e={host:process.env.SMTP_HOST||"smtp.gmail.com",port:parseInt(process.env.SMTP_PORT||"587"),secure:"true"===process.env.SMTP_SECURE,auth:{user:process.env.SMTP_USER,pass:process.env.SMTP_PASS}};if(!process.env.SMTP_USER||!process.env.SMTP_PASS)return console.warn("⚠️  SMTP credentials not configured. Emails will be logged to console."),null;try{return r.createTransporter(e)}catch(e){return console.error("Failed to create SMTP transporter:",e),null}}async function a({to:e,subject:t,html:o,text:r,from:a}){let s=n(),i={from:a||process.env.SMTP_FROM||"<EMAIL>",to:e,subject:t,html:o,text:r||o?.replace(/<[^>]*>/g,"")};if(!s)return console.log("\n\uD83D\uDCE7 EMAIL WOULD BE SENT:"),console.log("To:",e),console.log("Subject:",t),console.log("Content:",r||o),console.log("---\n"),{success:!0,messageId:"console-log-"+Date.now()};try{let e=await s.sendMail(i);return console.log("✅ Email sent successfully:",e.messageId),{success:!0,messageId:e.messageId}}catch(e){return console.error("❌ Failed to send email:",e),{success:!1,error:e.message}}}async function s(){let e=n();if(!e)return{success:!1,error:"SMTP not configured"};try{return await e.verify(),{success:!0,message:"SMTP connection verified"}}catch(e){return{success:!1,error:e.message}}}async function i(e){let t=`
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #1e40af;">Ocean Soul Sparkles Admin</h2>
      <p>This is a test email to verify your email configuration is working correctly.</p>
      <p>If you received this email, your SMTP settings are properly configured!</p>
      <hr style="margin: 20px 0; border: none; border-top: 1px solid #e5e7eb;">
      <p style="color: #6b7280; font-size: 14px;">
        Sent from Ocean Soul Sparkles Admin Dashboard<br>
        ${new Date().toLocaleString()}
      </p>
    </div>
  `;return await a({to:e,subject:"Ocean Soul Sparkles - Email Test",html:t})}e.exports={sendEmail:a,verifyConnection:s,sendTestEmail:i,createTransporter:n}},3306:e=>{function t(e,t="Ocean Soul Sparkles"){return`
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${t}</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
    .content { background: white; padding: 30px; border: 1px solid #e5e7eb; }
    .footer { background: #f9fafb; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 14px; color: #6b7280; }
    .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
    .alert { padding: 15px; border-radius: 6px; margin: 15px 0; }
    .alert-info { background: #dbeafe; border-left: 4px solid #3b82f6; }
    .alert-success { background: #d1fae5; border-left: 4px solid #10b981; }
    .alert-warning { background: #fef3c7; border-left: 4px solid #f59e0b; }
    .booking-details { background: #f8fafc; padding: 20px; border-radius: 6px; margin: 15px 0; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>✨ Ocean Soul Sparkles</h1>
      <p>Face Painting • Hair Braiding • Glitter Art</p>
    </div>
    <div class="content">
      ${e}
    </div>
    <div class="footer">
      <p>Ocean Soul Sparkles Admin Dashboard</p>
      <p><EMAIL> | +61 XXX XXX XXX</p>
      <p><small>This email was sent automatically. Please do not reply to this email.</small></p>
    </div>
  </div>
</body>
</html>`}e.exports={baseTemplate:t,bookingConfirmationTemplate:function(e){return t(`
    <h2>Booking Confirmation</h2>
    <p>Dear ${e.customerName},</p>
    <p>Your booking has been confirmed! Here are the details:</p>
    
    <div class="booking-details">
      <h3>Booking Details</h3>
      <p><strong>Service:</strong> ${e.serviceName}</p>
      <p><strong>Artist:</strong> ${e.artistName}</p>
      <p><strong>Date:</strong> ${e.date}</p>
      <p><strong>Time:</strong> ${e.time}</p>
      <p><strong>Duration:</strong> ${e.duration} minutes</p>
      <p><strong>Location:</strong> ${e.location||"Studio"}</p>
      <p><strong>Total Amount:</strong> $${e.totalAmount}</p>
    </div>

    <div class="alert alert-info">
      <p><strong>Important:</strong> Please arrive 10 minutes before your appointment time.</p>
    </div>

    <p>If you need to reschedule or cancel, please contact us at least 24 hours in advance.</p>
    <p>We look forward to creating something magical for you!</p>
  `,"Booking Confirmation - Ocean Soul Sparkles")},bookingReminderTemplate:function(e){return t(`
    <h2>Booking Reminder</h2>
    <p>Dear ${e.customerName},</p>
    <p>This is a friendly reminder about your upcoming appointment:</p>
    
    <div class="booking-details">
      <h3>Tomorrow's Appointment</h3>
      <p><strong>Service:</strong> ${e.serviceName}</p>
      <p><strong>Artist:</strong> ${e.artistName}</p>
      <p><strong>Date:</strong> ${e.date}</p>
      <p><strong>Time:</strong> ${e.time}</p>
      <p><strong>Location:</strong> ${e.location||"Studio"}</p>
    </div>

    <div class="alert alert-warning">
      <p><strong>Reminder:</strong> Please arrive 10 minutes early and bring any reference images if you have them.</p>
    </div>

    <p>Can't wait to see you tomorrow!</p>
  `,"Appointment Reminder - Ocean Soul Sparkles")},bookingCancellationTemplate:function(e){return t(`
    <h2>Booking Cancellation</h2>
    <p>Dear ${e.customerName},</p>
    <p>We're sorry to confirm that your booking has been cancelled:</p>
    
    <div class="booking-details">
      <h3>Cancelled Booking</h3>
      <p><strong>Service:</strong> ${e.serviceName}</p>
      <p><strong>Date:</strong> ${e.date}</p>
      <p><strong>Time:</strong> ${e.time}</p>
      <p><strong>Reason:</strong> ${e.cancellationReason||"Not specified"}</p>
    </div>

    ${e.refundAmount?`
    <div class="alert alert-success">
      <p><strong>Refund:</strong> $${e.refundAmount} will be processed within 3-5 business days.</p>
    </div>
    `:""}

    <p>We apologize for any inconvenience. Please feel free to book another appointment when convenient.</p>
  `,"Booking Cancellation - Ocean Soul Sparkles")},paymentReceiptTemplate:function(e){return t(`
    <h2>Payment Receipt</h2>
    <p>Dear ${e.customerName},</p>
    <p>Thank you for your payment! Here's your receipt:</p>
    
    <div class="booking-details">
      <h3>Payment Details</h3>
      <p><strong>Receipt #:</strong> ${e.receiptNumber}</p>
      <p><strong>Date:</strong> ${e.date}</p>
      <p><strong>Service:</strong> ${e.serviceName}</p>
      <p><strong>Amount Paid:</strong> $${e.amount}</p>
      <p><strong>Payment Method:</strong> ${e.method}</p>
      <p><strong>Transaction ID:</strong> ${e.transactionId}</p>
    </div>

    <div class="alert alert-success">
      <p>Payment processed successfully!</p>
    </div>

    <p>Keep this receipt for your records.</p>
  `,"Payment Receipt - Ocean Soul Sparkles")},staffNotificationTemplate:function(e){return t(`
    <h2>Staff Notification</h2>
    <p>Dear ${e.staffName},</p>
    <p>${e.message}</p>
    
    ${e.details?`
    <div class="booking-details">
      <h3>Details</h3>
      ${e.details}
    </div>
    `:""}

    ${e.actionRequired?`
    <div class="alert alert-warning">
      <p><strong>Action Required:</strong> ${e.actionRequired}</p>
    </div>
    `:""}

    <p>Please check the admin dashboard for more information.</p>
    <a href="http://localhost:3002/admin" class="button">Open Admin Dashboard</a>
  `,"Staff Notification - Ocean Soul Sparkles")},lowInventoryAlertTemplate:function(e){let o=e.map(e=>`<li><strong>${e.name}</strong> - ${e.currentStock} remaining (minimum: ${e.minStock})</li>`).join("");return t(`
    <h2>Low Inventory Alert</h2>
    <p>The following items are running low and need to be restocked:</p>
    
    <div class="alert alert-warning">
      <h3>Items Requiring Attention</h3>
      <ul>
        ${o}
      </ul>
    </div>

    <p>Please review and reorder these items to avoid stockouts.</p>
    <a href="http://localhost:3002/admin/inventory" class="button">Manage Inventory</a>
  `,"Low Inventory Alert - Ocean Soul Sparkles")}}}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var o=e=>t(t.s=e),r=t.X(0,[2805],()=>o(5254));module.exports=r})();