"use strict";(()=>{var e={};e.id=5249,e.ids=[5249],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},5875:(e,r,t)=>{t.r(r),t.d(r,{config:()=>m,default:()=>p,routeModule:()=>h});var a={};t.r(a),t.d(a,{default:()=>d});var s=t(1802),o=t(7153),n=t(8781),i=t(7474),u=t(6482);async function d(e,r){let t=`purchase-orders-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;try{console.log(`[${t}] Purchase Orders API request:`,{method:e.method,query:e.query,userAgent:e.headers["user-agent"]});let a=await (0,i.Wg)(e);if(!a.valid)return console.log(`[${t}] Authentication failed:`,a.error),r.status(401).json({error:"Unauthorized",requestId:t});if(!["DEV","Admin"].includes(a.user.role))return console.log(`[${t}] Insufficient permissions:`,a.user.role),r.status(403).json({error:"Insufficient permissions",requestId:t});if("GET"===e.method)return await l(e,r,t);if("POST"===e.method)return await c(e,r,t,a.user);return r.status(405).json({error:"Method not allowed",requestId:t})}catch(e){return console.error(`[${t}] Purchase Orders API error:`,e),r.status(500).json({error:"Internal server error",message:e.message,requestId:t})}}async function l(e,r,t){try{let{search:a="",status:s="all",supplier:o="all",page:n=1,limit:i=50,sortBy:d="order_date",sortOrder:l="desc"}=e.query,c=u.supabaseAdmin.from("purchase_orders").select(`
        *,
        suppliers (
          id,
          name,
          contact_person,
          email,
          phone
        ),
        admin_users!purchase_orders_created_by_fkey (
          id,
          first_name,
          last_name,
          email
        )
      `,{count:"exact"});a&&(c=c.or(`po_number.ilike.%${a}%,notes.ilike.%${a}%`)),"all"!==s&&(c=c.eq("status",s)),"all"!==o&&(c=c.eq("supplier_id",o));let p=["po_number","order_date","expected_delivery_date","total_amount","status"].includes(d)?d:"order_date";c=c.order(p,{ascending:"asc"===l});let m=Math.max(1,parseInt(n)),h=Math.min(100,Math.max(1,parseInt(i))),f=(m-1)*h;c=c.range(f,f+h-1);let{data:_,error:g,count:b}=await c;if(g)throw console.error(`[${t}] Database error:`,g),g;let I=Math.ceil(b/h);return console.log(`[${t}] Purchase orders fetched successfully:`,{count:_?.length||0,total:b,page:m,totalPages:I}),r.status(200).json({purchaseOrders:_||[],pagination:{page:m,limit:h,total:b,totalPages:I,hasNextPage:m<I,hasPrevPage:m>1},requestId:t})}catch(e){throw console.error(`[${t}] Error fetching purchase orders:`,e),e}}async function c(e,r,t,a){try{let{supplierId:s,orderDate:o,expectedDeliveryDate:n,notes:i,items:d=[]}=e.body;if(!s)return r.status(400).json({error:"Validation failed",message:"Supplier ID is required",requestId:t});if(!d||0===d.length)return r.status(400).json({error:"Validation failed",message:"At least one item is required",requestId:t});let{data:l,error:c}=await u.supabaseAdmin.from("suppliers").select("id, name, is_active").eq("id",s).single();if(c||!l)return r.status(404).json({error:"Supplier not found",requestId:t});if(!l.is_active)return r.status(400).json({error:"Validation failed",message:"Cannot create purchase order for inactive supplier",requestId:t});let p=0,m=[];for(let e of d){if(!e.inventoryId||!e.quantity||!e.unitCost)return r.status(400).json({error:"Validation failed",message:"All items must have inventory ID, quantity, and unit cost",requestId:t});let{data:a,error:s}=await u.supabaseAdmin.from("inventory").select("id, name, is_active").eq("id",e.inventoryId).single();if(s||!a)return r.status(404).json({error:"Inventory item not found",message:`Inventory item ${e.inventoryId} not found`,requestId:t});let o=parseInt(e.quantity),n=parseFloat(e.unitCost),i=o*n;m.push({inventory_id:e.inventoryId,product_name:a.name,quantity:o,unit_cost:n,total_cost:i}),p+=i}let h=.1*p,f=p+h,{data:_,error:g}=await u.supabaseAdmin.rpc("generate_po_number");if(g)throw console.error(`[${t}] Error generating PO number:`,g),g;let b={po_number:_,supplier_id:s,status:"draft",order_date:o||new Date().toISOString().split("T")[0],expected_delivery_date:n||null,subtotal:p,tax_amount:h,total_amount:f,notes:i?.trim()||null,created_by:a.id,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:I,error:y}=await u.supabaseAdmin.from("purchase_orders").insert([b]).select().single();if(y)throw console.error(`[${t}] Database error creating purchase order:`,y),y;let v=m.map(e=>({...e,purchase_order_id:I.id})),{data:w,error:q}=await u.supabaseAdmin.from("purchase_order_items").insert(v).select();if(q)throw console.error(`[${t}] Database error creating purchase order items:`,q),await u.supabaseAdmin.from("purchase_orders").delete().eq("id",I.id),q;return console.log(`[${t}] Purchase order created successfully:`,{id:I.id,poNumber:I.po_number,supplier:l.name,totalAmount:I.total_amount,itemCount:w.length,createdBy:a.email}),r.status(201).json({purchaseOrder:{...I,items:w,supplier:l},message:"Purchase order created successfully",requestId:t})}catch(e){throw console.error(`[${t}] Error creating purchase order:`,e),e}}let p=(0,n.l)(a,"default"),m=(0,n.l)(a,"config"),h=new s.PagesAPIRouteModule({definition:{kind:o.x.PAGES_API,page:"/api/admin/purchase-orders",pathname:"/api/admin/purchase-orders",bundlePath:"",filename:""},userland:a})},6482:(e,r,t)=>{t.r(r),t.d(r,{supabaseAdmin:()=>n});var a=t(2885);let s="https://ndlgbcsbidyhxbpqzgqp.supabase.co",o=process.env.SUPABASE_SERVICE_ROLE_KEY;s&&o||console.warn("Missing Supabase environment variables for admin client");let n=(0,a.createClient)(s,o||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",{auth:{autoRefreshToken:!1,persistSession:!1}})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[2805],()=>t(5875));module.exports=a})();