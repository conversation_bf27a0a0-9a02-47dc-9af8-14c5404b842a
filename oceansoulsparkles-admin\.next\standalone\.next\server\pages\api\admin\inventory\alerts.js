"use strict";(()=>{var e={};e.id=4315,e.ids=[4315],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5184:e=>{e.exports=require("nodemailer")},9200:e=>{e.exports=require("speakeasy")},7202:e=>{e.exports=require("twilio")},9575:(e,t,o)=>{o.r(t),o.d(t,{config:()=>g,default:()=>m,routeModule:()=>f});var r={};o.r(r),o.d(r,{default:()=>c,runScheduledAlertCheck:()=>p});var n=o(1802),s=o(7153),a=o(8781),i=o(7474),l=o(2393);async function c(e,t){let o=`alerts-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;try{console.log(`[${o}] Inventory alerts API request:`,{method:e.method,query:e.query,userAgent:e.headers["user-agent"]});let r=await (0,i.Wg)(e);if(!r.valid)return console.log(`[${o}] Authentication failed:`,r.error),t.status(401).json({error:"Unauthorized",requestId:o});if(!["DEV","Admin","Artist","Braider"].includes(r.user.role))return console.log(`[${o}] Insufficient permissions:`,r.user.role),t.status(403).json({error:"Insufficient permissions",requestId:o});if("GET"===e.method)return await d(e,t,o);if("POST"===e.method){if(!["DEV","Admin"].includes(r.user.role))return t.status(403).json({error:"Insufficient permissions for this operation",requestId:o});return await u(e,t,o,r.user)}return t.status(405).json({error:"Method not allowed",requestId:o})}catch(e){return console.error(`[${o}] Inventory alerts API error:`,e),t.status(500).json({error:"Internal server error",message:e.message,requestId:o})}}async function d(e,t,o){try{let e=(await (0,l.getActiveInventoryAlerts)()).map(e=>({id:e.id,alertType:e.alert_type,thresholdValue:e.threshold_value,currentValue:e.current_value,createdAt:e.created_at,severity:"out_of_stock"===e.alert_type?"critical":"warning",item:{id:e.inventory?.id,name:e.inventory?.name,sku:e.inventory?.sku,currentStock:e.inventory?.quantity_on_hand,minStockLevel:e.inventory?.min_stock_level}})),r=e.filter(e=>"critical"===e.severity),n=e.filter(e=>"warning"===e.severity);return console.log(`[${o}] Alerts fetched successfully:`,{total:e.length,critical:r.length,warning:n.length}),t.status(200).json({alerts:e,summary:{total:e.length,critical:r.length,warning:n.length},requestId:o})}catch(e){throw console.error(`[${o}] Error fetching alerts:`,e),e}}async function u(e,t,o,r){try{let{action:n,alertId:s}=e.body;if("check"===n){console.log(`[${o}] Manual alert check triggered by ${r.email}`);let e=await (0,l.checkLowStockAlerts)();return console.log(`[${o}] Alert check completed:`,e),t.status(200).json({message:"Alert check completed",result:e,requestId:o})}if("resolve"===n){if(!s)return t.status(400).json({error:"Alert ID is required for resolve action",requestId:o});console.log(`[${o}] Resolving alert ${s} by ${r.email}`);let e=await (0,l.resolveInventoryAlert)(s,r.id);return console.log(`[${o}] Alert resolved successfully:`,{alertId:e.id,resolvedBy:r.email}),t.status(200).json({message:"Alert resolved successfully",alert:e,requestId:o})}if("resolve_all"===n){console.log(`[${o}] Bulk resolving all alerts by ${r.email}`);let e=await (0,l.getActiveInventoryAlerts)(),n=e.length;for(let t of e)await (0,l.resolveInventoryAlert)(t.id,r.id);return console.log(`[${o}] Bulk resolve completed:`,{resolvedCount:n,resolvedBy:r.email}),t.status(200).json({message:`${n} alerts resolved successfully`,resolvedCount:n,requestId:o})}return t.status(400).json({error:"Invalid action. Supported actions: check, resolve, resolve_all",requestId:o})}catch(e){throw console.error(`[${o}] Error handling POST request:`,e),e}}async function p(){let e=`scheduled-${Date.now()}`;try{console.log(`[${e}] Running scheduled alert check`);let t=await (0,l.checkLowStockAlerts)();return console.log(`[${e}] Scheduled alert check completed:`,t),t}catch(t){throw console.error(`[${e}] Error in scheduled alert check:`,t),t}}let m=(0,a.l)(r,"default"),g=(0,a.l)(r,"config"),f=new n.PagesAPIRouteModule({definition:{kind:s.x.PAGES_API,page:"/api/admin/inventory/alerts",pathname:"/api/admin/inventory/alerts",bundlePath:"",filename:""},userland:r})},2393:(e,t,o)=>{let{supabaseAdmin:r}=o(6482),n=o(8173),s=o(5606);async function a(){let e=`alert-check-${Date.now()}`;try{console.log(`[${e}] Starting low stock alert check`);let{data:t,error:o}=await r.from("inventory").select("id, name, sku, quantity_on_hand, min_stock_level, reorder_point, supplier_id").eq("is_active",!0);if(o)throw console.error(`[${e}] Error fetching inventory:`,o),o;if(!t||0===t.length)return console.log(`[${e}] No inventory items found`),{alertsCreated:0,notificationsSent:0};let n=0,s=0;for(let o of t){let t=Math.max(o.min_stock_level||0,o.reorder_point||0),a=o.quantity_on_hand||0,l=null;if(0===a?l="out_of_stock":a<=t&&(l="low_stock"),l){let{data:c}=await r.from("inventory_alerts").select("id").eq("inventory_id",o.id).eq("alert_type",l).eq("is_active",!0).eq("is_resolved",!1).single();if(!c){let{data:c,error:d}=await r.from("inventory_alerts").insert([{inventory_id:o.id,alert_type:l,threshold_value:t,current_value:a,is_active:!0,is_resolved:!1}]).select().single();if(d){console.error(`[${e}] Error creating alert for ${o.name}:`,d);continue}n++,console.log(`[${e}] Created ${l} alert for ${o.name} (${a}/${t})`);try{await i(o,l,a,t,e),s++}catch(t){console.error(`[${e}] Error sending notifications for ${o.name}:`,t)}}}}return console.log(`[${e}] Alert check completed:`,{itemsChecked:t.length,alertsCreated:n,notificationsSent:s}),{alertsCreated:n,notificationsSent:s}}catch(t){throw console.error(`[${e}] Error in checkLowStockAlerts:`,t),t}}async function i(e,t,o,a,i){try{let{data:l}=await r.from("system_settings").select("key, value").in("key",["inventory_alerts_enabled","inventory_alert_email","inventory_alert_sms"]),c={};if(l?.forEach(e=>{c[e.key]=e.value}),"true"!==c.inventory_alerts_enabled){console.log(`[${i}] Inventory alerts disabled in settings`);return}let d="out_of_stock"===t?"Out of Stock Alert":"Low Stock Alert",u=`${d}: ${e.name} ${e.sku?`(${e.sku})`:""} - Current stock: ${o}, Threshold: ${a}`,p=c.inventory_alert_email;if(p){let t={subject:`${d} - ${e.name}`,html:`
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
              <h1 style="margin: 0; font-size: 24px;">${d}</h1>
            </div>
            <div style="background: white; padding: 20px; border: 1px solid #e2e8f0; border-radius: 0 0 8px 8px;">
              <h2 style="color: #1e293b; margin-top: 0;">${e.name}</h2>
              ${e.sku?`<p><strong>SKU:</strong> ${e.sku}</p>`:""}
              <div style="background: #fee2e2; border: 1px solid #fca5a5; border-radius: 6px; padding: 15px; margin: 15px 0;">
                <p style="margin: 0; color: #991b1b;">
                  <strong>Current Stock:</strong> ${o} units<br>
                  <strong>Threshold:</strong> ${a} units
                </p>
              </div>
              <p>This item needs to be restocked to maintain adequate inventory levels.</p>
              <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e2e8f0;">
                <p style="color: #64748b; font-size: 14px; margin: 0;">
                  Ocean Soul Sparkles Inventory Management System<br>
                  Generated at ${new Date().toLocaleString("en-AU")}
                </p>
              </div>
            </div>
          </div>
        `,text:u};await s.sendEmail(p,t.subject,t.html,t.text),console.log(`[${i}] Email notification sent for ${e.name}`)}let m=c.inventory_alert_sms;m&&(await n.sendSMS({to:m,message:u,type:"inventory_alert"}),console.log(`[${i}] SMS notification sent for ${e.name}`))}catch(e){throw console.error(`[${i}] Error sending notifications:`,e),e}}async function l(){try{let{data:e,error:t}=await r.from("inventory_alerts").select(`
        id,
        alert_type,
        threshold_value,
        current_value,
        created_at,
        inventory (
          id,
          name,
          sku,
          quantity_on_hand,
          min_stock_level
        )
      `).eq("is_active",!0).eq("is_resolved",!1).order("created_at",{ascending:!1});if(t)throw t;return e||[]}catch(e){throw console.error("Error fetching inventory alerts:",e),e}}async function c(e,t){try{let{data:o,error:n}=await r.from("inventory_alerts").update({is_resolved:!0,resolved_at:new Date().toISOString(),resolved_by:t}).eq("id",e).select().single();if(n)throw n;return o}catch(e){throw console.error("Error resolving inventory alert:",e),e}}async function d(e,t){try{let{data:o,error:n}=await r.from("inventory").update({quantity_on_hand:t,updated_at:new Date().toISOString()}).eq("id",e).select("id, name, quantity_on_hand, min_stock_level, reorder_point").single();if(n)throw n;let s=Math.max(o.min_stock_level||0,o.reorder_point||0);return t>s&&(await r.from("inventory_alerts").update({is_resolved:!0,resolved_at:new Date().toISOString()}).eq("inventory_id",e).eq("is_active",!0).eq("is_resolved",!1).in("alert_type",["low_stock","out_of_stock"]),console.log(`Resolved alerts for ${o.name} - stock updated to ${t}`)),o}catch(e){throw console.error("Error updating inventory stock:",e),e}}e.exports={checkLowStockAlerts:a,getActiveInventoryAlerts:l,resolveInventoryAlert:c,updateInventoryStock:d}},5606:(e,t,o)=>{let{sendEmail:r,verifyConnection:n,sendTestEmail:s}=o(3353),{bookingConfirmationTemplate:a,bookingReminderTemplate:i,bookingCancellationTemplate:l,paymentReceiptTemplate:c,staffNotificationTemplate:d,lowInventoryAlertTemplate:u}=o(3306);class p{constructor(){this.isConfigured=!!(process.env.SMTP_USER&&process.env.SMTP_PASS)}async checkEmailEnabled(e=null){try{let t=await fetch(`${process.env.NEXT_PUBLIC_BASE_URL||"http://localhost:3000"}/api/admin/settings`),{settings:o}=await t.json(),r=o?.notifications||{};if(!r.emailNotifications)return{enabled:!1,reason:"Email notifications disabled globally"};if(e){let t=`email${e.charAt(0).toUpperCase()+e.slice(1)}`;if(!1===r[t])return{enabled:!1,reason:`Email ${e} notifications disabled`}}return{enabled:!0}}catch(e){return console.error("Error checking email settings:",e),{enabled:!0}}}async sendBookingConfirmation(e){let t=await this.checkEmailEnabled("bookingConfirmation");if(!t.enabled)return console.log(`Email booking confirmation skipped: ${t.reason}`),{success:!1,skipped:!0,reason:t.reason};if(!e.customerEmail)return console.warn("No customer email provided for booking confirmation"),{success:!1,error:"No customer email"};let o=a(e);return await r({to:e.customerEmail,subject:`Booking Confirmation - ${e.serviceName}`,html:o})}async sendBookingReminder(e){let t=await this.checkEmailEnabled("bookingReminder");if(!t.enabled)return console.log(`Email booking reminder skipped: ${t.reason}`),{success:!1,skipped:!0,reason:t.reason};if(!e.customerEmail)return console.warn("No customer email provided for booking reminder"),{success:!1,error:"No customer email"};let o=i(e);return await r({to:e.customerEmail,subject:`Appointment Reminder - Tomorrow at ${e.time}`,html:o})}async sendBookingCancellation(e){if(!e.customerEmail)return console.warn("No customer email provided for booking cancellation"),{success:!1,error:"No customer email"};let t=l(e);return await r({to:e.customerEmail,subject:`Booking Cancellation - ${e.serviceName}`,html:t})}async sendPaymentReceipt(e){if(!e.customerEmail)return console.warn("No customer email provided for payment receipt"),{success:!1,error:"No customer email"};let t=c(e);return await r({to:e.customerEmail,subject:`Payment Receipt - ${e.receiptNumber}`,html:t})}async sendStaffNotification(e){if(!e.staffEmail)return console.warn("No staff email provided for notification"),{success:!1,error:"No staff email"};let t=d(e);return await r({to:e.staffEmail,subject:e.subject||"Staff Notification",html:t})}async sendLowInventoryAlert(e,t){t||(t=process.env.ADMIN_EMAIL||"<EMAIL>");let o=u(e);return await r({to:t,subject:`Low Inventory Alert - ${e.length} items need attention`,html:o})}async sendBulkEmail(e,t,o){let n=[];for(let s of e)try{let e=await r({to:s.email,subject:t,html:o.replace(/{{name}}/g,s.name||"Valued Customer")});n.push({email:s.email,...e}),await new Promise(e=>setTimeout(e,1e3))}catch(e){n.push({email:s.email,success:!1,error:e.message})}return n}async verifyConfiguration(){return await n()}async sendTest(e){return await s(e)}getStatus(){return{configured:this.isConfigured,smtpHost:process.env.SMTP_HOST||"Not configured",smtpUser:process.env.SMTP_USER?"Configured":"Not configured",smtpPort:process.env.SMTP_PORT||"587"}}}let m=new p;e.exports=m},3353:(e,t,o)=>{let r=o(5184);function n(){let e={host:process.env.SMTP_HOST||"smtp.gmail.com",port:parseInt(process.env.SMTP_PORT||"587"),secure:"true"===process.env.SMTP_SECURE,auth:{user:process.env.SMTP_USER,pass:process.env.SMTP_PASS}};if(!process.env.SMTP_USER||!process.env.SMTP_PASS)return console.warn("⚠️  SMTP credentials not configured. Emails will be logged to console."),null;try{return r.createTransporter(e)}catch(e){return console.error("Failed to create SMTP transporter:",e),null}}async function s({to:e,subject:t,html:o,text:r,from:s}){let a=n(),i={from:s||process.env.SMTP_FROM||"<EMAIL>",to:e,subject:t,html:o,text:r||o?.replace(/<[^>]*>/g,"")};if(!a)return console.log("\n\uD83D\uDCE7 EMAIL WOULD BE SENT:"),console.log("To:",e),console.log("Subject:",t),console.log("Content:",r||o),console.log("---\n"),{success:!0,messageId:"console-log-"+Date.now()};try{let e=await a.sendMail(i);return console.log("✅ Email sent successfully:",e.messageId),{success:!0,messageId:e.messageId}}catch(e){return console.error("❌ Failed to send email:",e),{success:!1,error:e.message}}}async function a(){let e=n();if(!e)return{success:!1,error:"SMTP not configured"};try{return await e.verify(),{success:!0,message:"SMTP connection verified"}}catch(e){return{success:!1,error:e.message}}}async function i(e){let t=`
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #1e40af;">Ocean Soul Sparkles Admin</h2>
      <p>This is a test email to verify your email configuration is working correctly.</p>
      <p>If you received this email, your SMTP settings are properly configured!</p>
      <hr style="margin: 20px 0; border: none; border-top: 1px solid #e5e7eb;">
      <p style="color: #6b7280; font-size: 14px;">
        Sent from Ocean Soul Sparkles Admin Dashboard<br>
        ${new Date().toLocaleString()}
      </p>
    </div>
  `;return await s({to:e,subject:"Ocean Soul Sparkles - Email Test",html:t})}e.exports={sendEmail:s,verifyConnection:a,sendTestEmail:i,createTransporter:n}},3306:e=>{function t(e,t="Ocean Soul Sparkles"){return`
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${t}</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
    .content { background: white; padding: 30px; border: 1px solid #e5e7eb; }
    .footer { background: #f9fafb; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 14px; color: #6b7280; }
    .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
    .alert { padding: 15px; border-radius: 6px; margin: 15px 0; }
    .alert-info { background: #dbeafe; border-left: 4px solid #3b82f6; }
    .alert-success { background: #d1fae5; border-left: 4px solid #10b981; }
    .alert-warning { background: #fef3c7; border-left: 4px solid #f59e0b; }
    .booking-details { background: #f8fafc; padding: 20px; border-radius: 6px; margin: 15px 0; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>✨ Ocean Soul Sparkles</h1>
      <p>Face Painting • Hair Braiding • Glitter Art</p>
    </div>
    <div class="content">
      ${e}
    </div>
    <div class="footer">
      <p>Ocean Soul Sparkles Admin Dashboard</p>
      <p><EMAIL> | +61 XXX XXX XXX</p>
      <p><small>This email was sent automatically. Please do not reply to this email.</small></p>
    </div>
  </div>
</body>
</html>`}e.exports={baseTemplate:t,bookingConfirmationTemplate:function(e){return t(`
    <h2>Booking Confirmation</h2>
    <p>Dear ${e.customerName},</p>
    <p>Your booking has been confirmed! Here are the details:</p>
    
    <div class="booking-details">
      <h3>Booking Details</h3>
      <p><strong>Service:</strong> ${e.serviceName}</p>
      <p><strong>Artist:</strong> ${e.artistName}</p>
      <p><strong>Date:</strong> ${e.date}</p>
      <p><strong>Time:</strong> ${e.time}</p>
      <p><strong>Duration:</strong> ${e.duration} minutes</p>
      <p><strong>Location:</strong> ${e.location||"Studio"}</p>
      <p><strong>Total Amount:</strong> $${e.totalAmount}</p>
    </div>

    <div class="alert alert-info">
      <p><strong>Important:</strong> Please arrive 10 minutes before your appointment time.</p>
    </div>

    <p>If you need to reschedule or cancel, please contact us at least 24 hours in advance.</p>
    <p>We look forward to creating something magical for you!</p>
  `,"Booking Confirmation - Ocean Soul Sparkles")},bookingReminderTemplate:function(e){return t(`
    <h2>Booking Reminder</h2>
    <p>Dear ${e.customerName},</p>
    <p>This is a friendly reminder about your upcoming appointment:</p>
    
    <div class="booking-details">
      <h3>Tomorrow's Appointment</h3>
      <p><strong>Service:</strong> ${e.serviceName}</p>
      <p><strong>Artist:</strong> ${e.artistName}</p>
      <p><strong>Date:</strong> ${e.date}</p>
      <p><strong>Time:</strong> ${e.time}</p>
      <p><strong>Location:</strong> ${e.location||"Studio"}</p>
    </div>

    <div class="alert alert-warning">
      <p><strong>Reminder:</strong> Please arrive 10 minutes early and bring any reference images if you have them.</p>
    </div>

    <p>Can't wait to see you tomorrow!</p>
  `,"Appointment Reminder - Ocean Soul Sparkles")},bookingCancellationTemplate:function(e){return t(`
    <h2>Booking Cancellation</h2>
    <p>Dear ${e.customerName},</p>
    <p>We're sorry to confirm that your booking has been cancelled:</p>
    
    <div class="booking-details">
      <h3>Cancelled Booking</h3>
      <p><strong>Service:</strong> ${e.serviceName}</p>
      <p><strong>Date:</strong> ${e.date}</p>
      <p><strong>Time:</strong> ${e.time}</p>
      <p><strong>Reason:</strong> ${e.cancellationReason||"Not specified"}</p>
    </div>

    ${e.refundAmount?`
    <div class="alert alert-success">
      <p><strong>Refund:</strong> $${e.refundAmount} will be processed within 3-5 business days.</p>
    </div>
    `:""}

    <p>We apologize for any inconvenience. Please feel free to book another appointment when convenient.</p>
  `,"Booking Cancellation - Ocean Soul Sparkles")},paymentReceiptTemplate:function(e){return t(`
    <h2>Payment Receipt</h2>
    <p>Dear ${e.customerName},</p>
    <p>Thank you for your payment! Here's your receipt:</p>
    
    <div class="booking-details">
      <h3>Payment Details</h3>
      <p><strong>Receipt #:</strong> ${e.receiptNumber}</p>
      <p><strong>Date:</strong> ${e.date}</p>
      <p><strong>Service:</strong> ${e.serviceName}</p>
      <p><strong>Amount Paid:</strong> $${e.amount}</p>
      <p><strong>Payment Method:</strong> ${e.method}</p>
      <p><strong>Transaction ID:</strong> ${e.transactionId}</p>
    </div>

    <div class="alert alert-success">
      <p>Payment processed successfully!</p>
    </div>

    <p>Keep this receipt for your records.</p>
  `,"Payment Receipt - Ocean Soul Sparkles")},staffNotificationTemplate:function(e){return t(`
    <h2>Staff Notification</h2>
    <p>Dear ${e.staffName},</p>
    <p>${e.message}</p>
    
    ${e.details?`
    <div class="booking-details">
      <h3>Details</h3>
      ${e.details}
    </div>
    `:""}

    ${e.actionRequired?`
    <div class="alert alert-warning">
      <p><strong>Action Required:</strong> ${e.actionRequired}</p>
    </div>
    `:""}

    <p>Please check the admin dashboard for more information.</p>
    <a href="http://localhost:3002/admin" class="button">Open Admin Dashboard</a>
  `,"Staff Notification - Ocean Soul Sparkles")},lowInventoryAlertTemplate:function(e){let o=e.map(e=>`<li><strong>${e.name}</strong> - ${e.currentStock} remaining (minimum: ${e.minStock})</li>`).join("");return t(`
    <h2>Low Inventory Alert</h2>
    <p>The following items are running low and need to be restocked:</p>
    
    <div class="alert alert-warning">
      <h3>Items Requiring Attention</h3>
      <ul>
        ${o}
      </ul>
    </div>

    <p>Please review and reorder these items to avoid stockouts.</p>
    <a href="http://localhost:3002/admin/inventory" class="button">Manage Inventory</a>
  `,"Low Inventory Alert - Ocean Soul Sparkles")}}},8173:(e,t,o)=>{let r=o(7202);class n{constructor(){this.isConfigured=!!(process.env.TWILIO_ACCOUNT_SID&&process.env.TWILIO_AUTH_TOKEN),this.client=this.isConfigured?r(process.env.TWILIO_ACCOUNT_SID,process.env.TWILIO_AUTH_TOKEN):null,this.fromNumber=process.env.TWILIO_PHONE_NUMBER||"+**********"}async checkSMSEnabled(e=null){try{let t=await fetch(`${process.env.NEXT_PUBLIC_BASE_URL||"http://localhost:3000"}/api/admin/settings`),{settings:o}=await t.json(),r=o?.notifications||{};if(!r.smsNotifications)return{enabled:!1,reason:"SMS notifications disabled globally"};if(e){let t=`sms${e.charAt(0).toUpperCase()+e.slice(1)}`;if(!1===r[t])return{enabled:!1,reason:`SMS ${e} notifications disabled`}}return{enabled:!0}}catch(e){return console.error("Error checking SMS settings:",e),{enabled:!1,reason:"Settings check failed"}}}async sendBookingConfirmation(e){let t=await this.checkSMSEnabled("bookingConfirmation");if(!t.enabled)return console.log(`SMS booking confirmation skipped: ${t.reason}`),{success:!1,skipped:!0,reason:t.reason};if(!e.customerPhone)return console.warn("No customer phone provided for booking confirmation SMS"),{success:!1,error:"No customer phone"};let o=`Hi ${e.customerName}! Your appointment for ${e.serviceName} is confirmed for ${e.date} at ${e.time}. Location: Ocean Soul Sparkles. Questions? Reply to this message.`;return await this.sendSMS({to:e.customerPhone,message:o,type:"booking_confirmation",bookingId:e.id})}async sendBookingReminder(e){let t=await this.checkSMSEnabled("bookingReminder");if(!t.enabled)return console.log(`SMS booking reminder skipped: ${t.reason}`),{success:!1,skipped:!0,reason:t.reason};if(!e.customerPhone)return console.warn("No customer phone provided for booking reminder SMS"),{success:!1,error:"No customer phone"};let o=`Reminder: Your appointment for ${e.serviceName} is tomorrow at ${e.time}. See you at Ocean Soul Sparkles! Reply CONFIRM to confirm or CANCEL to reschedule.`;return await this.sendSMS({to:e.customerPhone,message:o,type:"booking_reminder",bookingId:e.id})}async sendBookingCancellation(e){let t=await this.checkSMSEnabled("bookingCancellation");if(!t.enabled)return console.log(`SMS booking cancellation skipped: ${t.reason}`),{success:!1,skipped:!0,reason:t.reason};if(!e.customerPhone)return console.warn("No customer phone provided for booking cancellation SMS"),{success:!1,error:"No customer phone"};let o=`Your appointment for ${e.serviceName} on ${e.date} at ${e.time} has been cancelled. To reschedule, please contact Ocean Soul Sparkles. Thank you!`;return await this.sendSMS({to:e.customerPhone,message:o,type:"booking_cancellation",bookingId:e.id})}async sendStaffNotification(e){let t=await this.checkSMSEnabled("staffNotification");if(!t.enabled)return console.log(`SMS staff notification skipped: ${t.reason}`),{success:!1,skipped:!0,reason:t.reason};if(!e.staffPhone)return console.warn("No staff phone provided for notification SMS"),{success:!1,error:"No staff phone"};let o=e.message||`Staff notification: ${e.subject}`;return await this.sendSMS({to:e.staffPhone,message:o,type:"staff_notification",staffId:e.staffId})}async sendPromotionalSMS(e,t){let o=await this.checkSMSEnabled("promotional");return o.enabled?e.phone?await this.sendSMS({to:e.phone,message:t,type:"promotional",customerId:e.id}):(console.warn("No customer phone provided for promotional SMS"),{success:!1,error:"No customer phone"}):(console.log(`SMS promotional message skipped: ${o.reason}`),{success:!1,skipped:!0,reason:o.reason})}async sendSMS({to:e,message:t,type:o,bookingId:r,customerId:n,staffId:s}){let a=this.normalizePhoneNumber(e);if(!a)return{success:!1,error:"Invalid phone number format"};if(!this.client)return console.log("\n\uD83D\uDCF1 SMS WOULD BE SENT:"),console.log("To:",a),console.log("Message:",t),console.log("Type:",o),console.log("---\n"),{success:!0,messageId:"console-log-"+Date.now(),fallback:!0};try{let e=await this.client.messages.create({body:t,from:this.fromNumber,to:a});return console.log(`SMS sent successfully: ${e.sid}`),{success:!0,messageId:e.sid,status:e.status,to:a,type:o}}catch(e){return console.error("SMS sending failed:",e),{success:!1,error:e.message,code:e.code,to:a,type:o}}}async sendBulkSMS(e,t,o="bulk"){let r=await this.checkSMSEnabled(o);if(!r.enabled)return console.log(`Bulk SMS skipped: ${r.reason}`),{success:!1,skipped:!0,reason:r.reason};let n=[];for(let r of e)try{let e=await this.sendSMS({to:r.phone,message:t.replace(/{{name}}/g,r.name||"Valued Customer"),type:o,customerId:r.id});n.push({phone:r.phone,...e}),await new Promise(e=>setTimeout(e,1e3))}catch(e){n.push({phone:r.phone,success:!1,error:e.message})}return n}normalizePhoneNumber(e){if(!e)return null;let t=e.replace(/\D/g,"");return t.startsWith("61")?"+"+t:t.startsWith("0")&&10===t.length?"+61"+t.substring(1):9===t.length?"+61"+t:t.startsWith("1")&&11===t.length?"+"+t:t.length>=10?"+"+t:null}async verifyConfiguration(){if(!this.isConfigured)return{configured:!1,error:"Twilio credentials not configured"};try{let e=await this.client.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();return{configured:!0,accountSid:e.sid,status:e.status,fromNumber:this.fromNumber}}catch(e){return{configured:!1,error:e.message}}}getStatus(){return{configured:this.isConfigured,provider:"Twilio",fromNumber:this.fromNumber,environment:"production"}}}e.exports=new n},6482:(e,t,o)=>{o.r(t),o.d(t,{supabaseAdmin:()=>a});var r=o(2885);let n="https://ndlgbcsbidyhxbpqzgqp.supabase.co",s=process.env.SUPABASE_SERVICE_ROLE_KEY;n&&s||console.warn("Missing Supabase environment variables for admin client");let a=(0,r.createClient)(n,s||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",{auth:{autoRefreshToken:!1,persistSession:!1}})}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var o=e=>t(t.s=e),r=t.X(0,[2805],()=>o(9575));module.exports=r})();